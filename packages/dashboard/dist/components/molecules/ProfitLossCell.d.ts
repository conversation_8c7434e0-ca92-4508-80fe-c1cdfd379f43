/**
 * ProfitLossCell Component (REFACTORED)
 *
 * SIMPLIFIED VERSION: Extracted complex logic into hooks and theme files.
 * Now focuses solely on rendering with clean separation of concerns.
 */
import React from 'react';
export interface ProfitLossCellProps {
    /** The profit/loss amount to display */
    amount: number | null | undefined;
    /** Currency symbol to display (default: '$') */
    currency?: string;
    /** Size variant for different contexts */
    size?: 'small' | 'medium' | 'large';
    /** Whether to show the sign for positive numbers */
    showPositiveSign?: boolean;
    /** Custom className for styling */
    className?: string;
    /** Loading state */
    isLoading?: boolean;
    /** Accessibility label */
    'aria-label'?: string;
}
/**
 * ProfitLossCell Component
 *
 * Displays profit/loss amounts with appropriate color coding and formatting.
 * Designed for ADHD-friendly quick visual recognition in trading interfaces.
 */
export declare const ProfitLossCell: React.FC<ProfitLossCellProps>;
export default ProfitLossCell;
//# sourceMappingURL=ProfitLossCell.d.ts.map