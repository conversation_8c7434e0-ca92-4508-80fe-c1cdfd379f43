/**
 * Feature Error Boundary
 *
 * An error boundary for feature modules.
 * This is a simplified version that uses the unified error boundary approach.
 */
import React from 'react';
/**
 * Feature Error Boundary Props
 */
export interface FeatureErrorBoundaryProps {
    children: React.ReactNode;
    featureName: string;
    onError?: (error: Error) => void;
    onSkip?: () => void;
}
/**
 * Feature Error Boundary
 *
 * An error boundary for feature modules.
 */
export declare const FeatureErrorBoundary: React.FC<FeatureErrorBoundaryProps>;
export default FeatureErrorBoundary;
//# sourceMappingURL=FeatureErrorBoundary.d.ts.map