/**
 * App Error Boundary
 *
 * A top-level error boundary for the entire application.
 * This is a simplified version that uses the unified error boundary approach.
 */
import React from 'react';
/**
 * App Error Boundary Props
 */
export interface AppErrorBoundaryProps {
    children: React.ReactNode;
}
/**
 * App Error Boundary
 *
 * A top-level error boundary for the entire application.
 */
export declare const AppErrorBoundary: React.FC<AppErrorBoundaryProps>;
export default AppErrorBoundary;
//# sourceMappingURL=AppErrorBoundary.d.ts.map