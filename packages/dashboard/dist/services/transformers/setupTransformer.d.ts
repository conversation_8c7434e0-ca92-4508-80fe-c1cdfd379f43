/**
 * Setup Transformer Service
 *
 * Transforms setup components to human-readable descriptions and handles
 * backward compatibility with existing string-based setup descriptions.
 */
import { SetupComponents } from '@adhd-trading-dashboard/shared';
export declare class SetupTransformer {
    /**
     * Convert setup components to a human-readable description
     * @param components The setup components to transform
     * @returns A formatted setup description string
     */
    static componentsToDescription(components: SetupComponents): string;
    /**
     * Parse a setup description string back to components (for backward compatibility)
     * @param description The setup description string to parse
     * @returns Parsed setup components or null if parsing fails
     */
    static descriptionToComponents(description: string): SetupComponents | null;
    /**
     * Check if a setup description is in the new component format
     * @param description The setup description to check
     * @returns True if it appears to be in component format
     */
    static isComponentFormat(description: string): boolean;
    /**
     * Get a display-friendly version of setup components
     * @param components The setup components
     * @returns A formatted display string
     */
    static getDisplayString(components: SetupComponents): string;
    /**
     * Convert setup components to a short display string for tables
     * @param components The setup components
     * @returns A short formatted string for table display
     */
    static getShortDisplayString(components: SetupComponents): string;
    /**
     * Convert database fields back to SetupComponents
     * @param setup_constant Database constant field
     * @param setup_action Database action field
     * @param setup_variable Database variable field
     * @param setup_entry Database entry field
     * @returns SetupComponents object
     */
    static fromDatabaseFields(setup_constant?: string, setup_action?: string, setup_variable?: string, setup_entry?: string): SetupComponents | null;
    /**
     * Validate setup components
     * @param components The setup components to validate
     * @returns Validation result with errors if any
     */
    static validateComponents(components: SetupComponents): {
        isValid: boolean;
        errors: string[];
    };
    /**
     * Create a default setup components object
     * @returns Default setup components
     */
    static createDefault(): SetupComponents;
    /**
     * Clone setup components
     * @param components The components to clone
     * @returns A deep copy of the components
     */
    static clone(components: SetupComponents): SetupComponents;
}
//# sourceMappingURL=setupTransformer.d.ts.map