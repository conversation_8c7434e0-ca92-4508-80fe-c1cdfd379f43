{"version": 3, "file": "MetricsPanel.js", "sourceRoot": "", "sources": ["../../../../src/features/performance-dashboard/components/MetricsPanel.tsx"], "names": [], "mappings": ";AAOA,OAAO,MAAM,MAAM,mBAAmB,CAAC;AAYvC,MAAM,SAAS,GAAG,MAAM,CAAC,GAAG,CAAA;;;SAGnB,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;CACvC,CAAC;AAEF,MAAM,UAAU,GAAG,MAAM,CAAC,GAAG,CAAA;sBACP,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO;mBACtC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE;aAC1C,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;gBAC5B,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;CAC9C,CAAC;AAEF,MAAM,WAAW,GAAG,MAAM,CAAC,EAAE,CAAA;eACd,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE;WACrC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,aAAa;gBACpC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;CAC9C,CAAC;AAEF,MAAM,WAAW,GAAG,MAAM,CAAC,GAAG,CAAA;eACf,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE;;WAErC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,WAAW;CACjD,CAAC;AAEF,MAAM,gBAAgB,GAAG,MAAM,CAAC,GAAG,CAAA;WACxB,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,aAAa;;CAEnD,CAAC;AAEF,MAAM,CAAC,MAAM,YAAY,GAAgC,CAAC,EACxD,OAAO,EACP,SAAS,GAAG,KAAK,GAClB,EAAE,EAAE;IACH,IAAI,SAAS,EAAE,CAAC;QACd,OAAO,CACL,KAAC,SAAS,cACP,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CACvB,MAAC,UAAU,eACT,KAAC,WAAW,6BAAyB,EACrC,KAAC,gBAAgB,mCAAoC,KAFtC,CAAC,CAGL,CACd,CAAC,GACQ,CACb,CAAC;IACJ,CAAC;IAED,OAAO,CACL,KAAC,SAAS,cACP,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC,CAC9B,MAAC,UAAU,eACT,KAAC,WAAW,cAAE,MAAM,CAAC,KAAK,GAAe,EACzC,KAAC,WAAW,cAAE,MAAM,CAAC,KAAK,GAAe,KAF1B,KAAK,CAGT,CACd,CAAC,GACQ,CACb,CAAC;AACJ,CAAC,CAAC"}