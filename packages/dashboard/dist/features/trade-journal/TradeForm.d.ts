/**
 * Trade Form Page
 *
 * This page displays a form for adding or editing a trade entry.
 * Uses a single scrollable page layout with consolidated sections.
 *
 * REFACTORED: Consolidated duplicate sections and removed legacy components
 * - Unified Analysis section (Pattern Quality + DOL Analysis)
 * - Consolidated timing sections
 * - Removed duplicate pattern quality fields
 * - Modern setup construction with SetupBuilder
 */
import React from 'react';
/**
 * TradeForm Component
 *
 * Displays a form for adding or editing trade entries in a single scrollable page
 * with all sections displayed vertically for better workflow.
 */
declare const TradeForm: React.FC;
export default TradeForm;
//# sourceMappingURL=TradeForm.d.ts.map