/**
 * Setup Classification Constants
 *
 * Constants for the setup classification feature
 */
/**
 * Setup Category Options
 */
export declare const SETUP_CATEGORY_OPTIONS: {
    value: string;
    label: string;
}[];
/**
 * Structure-Based Setup Options
 */
export declare const STRUCTURE_SETUP_OPTIONS: {
    value: string;
    label: string;
}[];
/**
 * Session-Based Setup Options
 */
export declare const SESSION_SETUP_OPTIONS: {
    value: string;
    label: string;
}[];
/**
 * Model-Specific Setup Options
 */
export declare const MODEL_SETUP_OPTIONS: {
    value: string;
    label: string;
}[];
/**
 * Liquidity Options
 */
export declare const LIQUIDITY_OPTIONS: {
    value: string;
    label: string;
}[];
/**
 * FVG Type Options - Time-based FVGs
 */
export declare const TIME_BASED_FVG_OPTIONS: {
    value: string;
    label: string;
}[];
/**
 * FVG Type Options - Current Session FPFVGs
 */
export declare const CURRENT_SESSION_FVG_OPTIONS: {
    value: string;
    label: string;
}[];
/**
 * FVG Type Options - Previous Day FPFVGs
 */
export declare const PREV_DAY_FVG_OPTIONS: {
    value: string;
    label: string;
}[];
/**
 * FVG Type Options - 3Day FPFVGs
 */
export declare const THREE_DAY_FVG_OPTIONS: {
    value: string;
    label: string;
}[];
/**
 * FVG Type Options - Special FVGs
 */
export declare const SPECIAL_FVG_OPTIONS: {
    value: string;
    label: string;
}[];
/**
 * All FVG Options combined
 */
export declare const ALL_FVG_OPTIONS: {
    value: string;
    label: string;
}[];
/**
 * DOL Target Options
 */
export declare const DOL_TARGET_OPTIONS: {
    value: string;
    label: string;
}[];
/**
 * Parent PD Array Options
 */
export declare const PARENT_PD_ARRAY_OPTIONS: {
    value: string;
    label: string;
}[];
/**
 * Get setup options based on category
 */
export declare const getSetupOptionsByCategory: (category: string) => {
    value: string;
    label: string;
}[];
/**
 * Get DOL target options based on target type
 */
export declare const getDOLTargetOptions: (targetType: string) => {
    value: string;
    label: string;
}[];
//# sourceMappingURL=setupClassification.d.ts.map