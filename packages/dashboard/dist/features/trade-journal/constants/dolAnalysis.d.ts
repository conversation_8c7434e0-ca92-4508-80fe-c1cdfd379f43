/**
 * DOL (Draw on Liquidity) Analysis Constants
 *
 * Constants for the DOL analysis feature
 */
/**
 * DOL Type Options
 */
export declare const DOL_TYPE_OPTIONS: {
    value: string;
    label: string;
}[];
/**
 * DOL Strength Options
 */
export declare const DOL_STRENGTH_OPTIONS: {
    value: string;
    label: string;
}[];
/**
 * DOL Reaction Options
 */
export declare const DOL_REACTION_OPTIONS: {
    value: string;
    label: string;
}[];
/**
 * DOL Context Options
 */
export declare const DOL_CONTEXT_OPTIONS: {
    value: string;
    label: string;
}[];
/**
 * DOL Effectiveness Rating Options
 */
export declare const DOL_EFFECTIVENESS_OPTIONS: {
    value: string;
    label: string;
}[];
/**
 * DOL Price Action Descriptions
 */
export declare const DOL_PRICE_ACTION_DESCRIPTIONS: {
    Sweep: string;
    Tap: string;
    Approach: string;
    Rejection: string;
};
/**
 * DOL Volume Profile Descriptions
 */
export declare const DOL_VOLUME_PROFILE_DESCRIPTIONS: {
    Strong: string;
    Moderate: string;
    Weak: string;
};
/**
 * DOL Time of Day Significance
 */
export declare const DOL_TIME_OF_DAY_DESCRIPTION: string;
/**
 * DOL Market Structure Description
 */
export declare const DOL_MARKET_STRUCTURE_DESCRIPTION: string;
/**
 * Get description for DOL type
 */
export declare const getDOLTypeDescription: (dolType: string) => string;
/**
 * Get description for DOL strength
 */
export declare const getDOLStrengthDescription: (dolStrength: string) => string;
/**
 * Get description for DOL reaction
 */
export declare const getDOLReactionDescription: (dolReaction: string) => string;
/**
 * Get color for DOL effectiveness rating
 */
export declare const getDOLEffectivenessColor: (rating: number) => string;
/**
 * Get description for DOL effectiveness rating
 */
export declare const getDOLEffectivenessDescription: (rating: number) => string;
//# sourceMappingURL=dolAnalysis.d.ts.map