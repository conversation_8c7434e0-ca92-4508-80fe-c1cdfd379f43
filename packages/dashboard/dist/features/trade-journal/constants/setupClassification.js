/**
 * Setup Classification Constants
 *
 * Constants for the setup classification feature
 */
// Type imports removed as they are not used in this constants file
// import {
//   StructureSetupType,
//   SessionSetupType,
//   ModelSetupType,
//   LiquidityType,
//   FVGType,
//   DOLTargetType,
//   ParentPDArrayType
// } from '../types';
/**
 * Setup Category Options
 */
export const SETUP_CATEGORY_OPTIONS = [
  { value: 'structure', label: 'Structure-Based Setup' },
  { value: 'session', label: 'Session-Based Setup' },
  { value: 'model', label: 'Model-Specific Setup' },
];
/**
 * Structure-Based Setup Options
 */
export const STRUCTURE_SETUP_OPTIONS = [
  { value: 'High/Low Reversal Setup', label: 'High/Low Reversal Setup' },
  { value: 'FVG Redelivery Setup', label: 'FVG Redelivery Setup' },
  { value: 'Strong-FVG Reversal Setup', label: 'Strong-FVG Reversal Setup' },
  { value: 'NWOG Reaction Setup', label: 'NWOG Reaction Setup' },
  { value: 'NDOG Reaction Setup', label: 'NDOG Reaction Setup' },
  { value: 'Multi-Array Confluence Setup', label: 'Multi-Array Confluence Setup' },
];
/**
 * Session-Based Setup Options
 */
export const SESSION_SETUP_OPTIONS = [
  { value: 'Opening Range Setup (09:30-10:10)', label: 'Opening Range Setup (09:30-10:10)' },
  { value: 'Morning Session Setup (10:50-11:10)', label: 'Morning Session Setup (10:50-11:10)' },
  { value: 'Lunch Macro Setup (11:45-13:15)', label: 'Lunch Macro Setup (11:45-13:15)' },
  {
    value: 'Afternoon Session Setup (13:30-15:00)',
    label: 'Afternoon Session Setup (13:30-15:00)',
  },
  { value: 'MOC Setup (15:30-16:15)', label: 'MOC Setup (15:30-16:15)' },
];
/**
 * Model-Specific Setup Options
 */
export const MODEL_SETUP_OPTIONS = [
  { value: 'Simple FVG-RD', label: 'Simple FVG-RD' },
  { value: 'Complex FVG-RD', label: 'Complex FVG-RD' },
  { value: 'True-RD Continuation', label: 'True-RD Continuation' },
  { value: 'IMM-RD Continuation', label: 'IMM-RD Continuation' },
  { value: 'Dispersed-RD Continuation', label: 'Dispersed-RD Continuation' },
  { value: 'Wide-Gap-RD Continuation', label: 'Wide-Gap-RD Continuation' },
];
/**
 * Liquidity Options
 */
export const LIQUIDITY_OPTIONS = [
  { value: '', label: 'None/Not Applicable' },
  { value: 'London-H/L', label: 'London-H/L' },
  { value: 'Premarket-H/L', label: 'Premarket-H/L' },
  { value: '09:30-Opening-Range-H/L', label: '09:30-Opening-Range-H/L' },
  { value: 'Post-10:00am-Lunch-Macro-H/L', label: 'Post-10:00am-Lunch-Macro-H/L' },
  { value: 'Lunch-H/L', label: 'Lunch-H/L' },
  { value: 'Monthly-H/L', label: 'Monthly-H/L' },
  { value: 'Prev-Week-H/L', label: 'Prev-Week-H/L' },
  { value: 'Prev-Day-H/L', label: 'Prev-Day-H/L' },
  { value: 'Macro-H/L', label: 'Macro-H/L' },
];
/**
 * FVG Type Options - Time-based FVGs
 */
export const TIME_BASED_FVG_OPTIONS = [
  { value: 'Monthly-FVG', label: 'Monthly-FVG' },
  { value: 'Weekly-FVG', label: 'Weekly-FVG' },
  { value: 'Daily-FVG', label: 'Daily-FVG' },
  { value: 'Daily-Top/Bottom-FVG', label: 'Daily-Top/Bottom-FVG' },
  { value: '1h-Top/Bottom-FVG', label: '1h-Top/Bottom-FVG' },
  { value: '15min-Top/Bottom-FVG', label: '15min-Top/Bottom-FVG' },
];
/**
 * FVG Type Options - Current Session FPFVGs
 */
export const CURRENT_SESSION_FVG_OPTIONS = [
  { value: 'MNOR-FVG', label: 'MNOR-FVG' },
  { value: 'Asia-FPFVG', label: 'Asia-FPFVG' },
  { value: 'Premarket-FPFVG', label: 'Premarket-FPFVG' },
  { value: 'AM-FPFVG', label: 'AM-FPFVG' },
  { value: 'PM-FPFVG', label: 'PM-FPFVG' },
];
/**
 * FVG Type Options - Previous Day FPFVGs
 */
export const PREV_DAY_FVG_OPTIONS = [
  { value: 'Prev-Day-MNOR-FVG', label: 'Prev-Day-MNOR-FVG' },
  { value: 'Prev-Day-Asia-FPFVG', label: 'Prev-Day-Asia-FPFVG' },
  { value: 'Prev-Day-Premarket-FPFVG', label: 'Prev-Day-Premarket-FPFVG' },
  { value: 'Prev-Day-AM-FPFVG', label: 'Prev-Day-AM-FPFVG' },
  { value: 'Prev-Day-PM-FPFVG', label: 'Prev-Day-PM-FPFVG' },
];
/**
 * FVG Type Options - 3Day FPFVGs
 */
export const THREE_DAY_FVG_OPTIONS = [
  { value: '3Day-MNOR-FVG', label: '3Day-MNOR-FVG' },
  { value: '3Day-Asia-FPFVG', label: '3Day-Asia-FPFVG' },
  { value: '3Day-Premarket-FPFVG', label: '3Day-Premarket-FPFVG' },
  { value: '3Day-AM-FPFVG', label: '3Day-AM-FPFVG' },
  { value: '3Day-PM-FPFVG', label: '3Day-PM-FPFVG' },
];
/**
 * FVG Type Options - Special FVGs
 */
export const SPECIAL_FVG_OPTIONS = [
  { value: 'Top/Bottom-FVG', label: 'Top/Bottom-FVG' },
  { value: 'Macro-FVG', label: 'Macro-FVG' },
  { value: 'News-FVG', label: 'News-FVG' },
  { value: '10min-Prior-To-News-FVG', label: '10min-Prior-To-News-FVG' },
  { value: 'Strong-FVG', label: 'Strong-FVG' },
  { value: 'RDRB-FVG', label: 'RDRB-FVG' },
];
/**
 * All FVG Options combined
 */
export const ALL_FVG_OPTIONS = [
  ...TIME_BASED_FVG_OPTIONS,
  ...CURRENT_SESSION_FVG_OPTIONS,
  ...PREV_DAY_FVG_OPTIONS,
  ...THREE_DAY_FVG_OPTIONS,
  ...SPECIAL_FVG_OPTIONS,
];
/**
 * DOL Target Options
 */
export const DOL_TARGET_OPTIONS = [
  { value: '', label: 'None/Not Applicable' },
  { value: 'FVG Target', label: 'FVG Target' },
  { value: 'Liquidity Target', label: 'Liquidity Target' },
  { value: 'RD Target', label: 'RD Target' },
];
/**
 * Parent PD Array Options
 */
export const PARENT_PD_ARRAY_OPTIONS = [
  { value: '', label: 'None/Not Applicable' },
  { value: 'NWOG', label: 'NWOG' },
  { value: 'Old-NWOG', label: 'Old-NWOG' },
  { value: 'NDOG', label: 'NDOG' },
  { value: 'Old-NDOG', label: 'Old-NDOG' },
  { value: 'Monthly-FVG', label: 'Monthly-FVG' },
  { value: 'Weekly-FVG', label: 'Weekly-FVG' },
  { value: 'Daily-FVG', label: 'Daily-FVG' },
  { value: 'Daily-Top/Bottom-FVG', label: 'Daily-Top/Bottom-FVG' },
  { value: '1h-Top/Bottom-FVG', label: '1h-Top/Bottom-FVG' },
  { value: '15min-Top/Bottom-FVG', label: '15min-Top/Bottom-FVG' },
];
/**
 * Get setup options based on category
 */
export const getSetupOptionsByCategory = category => {
  switch (category) {
    case 'structure':
      return STRUCTURE_SETUP_OPTIONS;
    case 'session':
      return SESSION_SETUP_OPTIONS;
    case 'model':
      return MODEL_SETUP_OPTIONS;
    default:
      return [];
  }
};
/**
 * Get DOL target options based on target type
 */
export const getDOLTargetOptions = targetType => {
  switch (targetType) {
    case 'FVG Target':
      return ALL_FVG_OPTIONS;
    case 'Liquidity Target':
      return LIQUIDITY_OPTIONS;
    default:
      return [];
  }
};
//# sourceMappingURL=setupClassification.js.map
