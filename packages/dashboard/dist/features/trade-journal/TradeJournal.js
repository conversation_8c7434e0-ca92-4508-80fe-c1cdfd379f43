import { jsx as _jsx } from "react/jsx-runtime";
import { F1JournalContainer } from './components/F1JournalContainer';
/**
 * Trade Journal Component
 *
 * Simple wrapper that renders the container.
 * Follows the proven architecture pattern.
 */
const TradeJournal = ({ className, initialTab }) => {
    return _jsx(F1JournalContainer, { className: className, initialTab: initialTab });
};
export default TradeJournal;
//# sourceMappingURL=TradeJournal.js.map