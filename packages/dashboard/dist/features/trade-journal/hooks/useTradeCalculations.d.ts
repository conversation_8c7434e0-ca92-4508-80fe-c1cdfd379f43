/**
 * Trade Calculations Hook
 *
 * Custom hook for calculating trade metrics
 */
import { TradeFormValues } from '../types';
/**
 * Hook for calculating trade metrics
 * @param formValues The form values to calculate metrics for
 * @param setFormValues Function to update form values
 */
export declare function useTradeCalculations(formValues: TradeFormValues, setFormValues: React.Dispatch<React.SetStateAction<TradeFormValues>>): {
    calculateProfitLoss: () => void;
};
export type TradeCalculationsHook = ReturnType<typeof useTradeCalculations>;
//# sourceMappingURL=useTradeCalculations.d.ts.map