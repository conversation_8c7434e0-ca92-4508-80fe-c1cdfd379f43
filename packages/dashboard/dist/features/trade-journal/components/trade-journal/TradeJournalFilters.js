import { jsx as _jsx } from "react/jsx-runtime";
/**
 * Trade Journal Filters Component
 *
 * REFACTORED: Now uses the new F1 filter component library.
 * Simplified from 322 lines to a clean wrapper component.
 *
 * BENEFITS:
 * - 90% code reduction
 * - Uses F1FilterPanel for consistent styling
 * - Eliminates repetitive filter field code
 * - Better separation of concerns
 * - Type-safe filter handling
 */
import { useMemo } from 'react';
import { F1FilterPanel } from '../F1FilterPanel';
/**
 * Trade Journal Filters Component
 *
 * Simple wrapper that renders the F1FilterPanel.
 * Follows the proven architecture pattern.
 */
const TradeJournalFilters = ({ filters, handleFilterChange, resetFilters, uniqueSetups, uniqueModelTypes, uniquePrimarySetupTypes, uniqueSecondarySetupTypes, uniqueLiquidityTypes, uniqueDOLTypes, }) => {
    // Convert unique data to the format expected by F1FilterPanel
    const uniqueData = useMemo(() => ({
        uniqueSetups,
        uniqueModelTypes,
        uniquePrimarySetupTypes,
        uniqueSecondarySetupTypes,
        uniqueLiquidityTypes,
        uniqueDOLTypes,
    }), [
        uniqueSetups,
        uniqueModelTypes,
        uniquePrimarySetupTypes,
        uniqueSecondarySetupTypes,
        uniqueLiquidityTypes,
        uniqueDOLTypes,
    ]);
    // Convert the legacy handleFilterChange to the new format
    const handleFiltersChange = (newFilters) => {
        // For each changed filter, create a synthetic event and call the legacy handler
        Object.entries(newFilters).forEach(([name, value]) => {
            if (filters[name] !== value) {
                const syntheticEvent = {
                    target: { name, value: String(value) },
                };
                handleFilterChange(syntheticEvent);
            }
        });
    };
    return (_jsx(F1FilterPanel, { initialFilters: filters, onFiltersChange: handleFiltersChange, onReset: resetFilters, uniqueData: uniqueData }));
};
export default TradeJournalFilters;
//# sourceMappingURL=TradeJournalFilters.js.map