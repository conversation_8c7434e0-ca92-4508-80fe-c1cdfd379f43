/**
 * Trade Journal Filters Component
 *
 * REFACTORED: Now uses the new F1 filter component library.
 * Simplified from 322 lines to a clean wrapper component.
 *
 * BENEFITS:
 * - 90% code reduction
 * - Uses F1FilterPanel for consistent styling
 * - Eliminates repetitive filter field code
 * - Better separation of concerns
 * - Type-safe filter handling
 */
import React from 'react';
import { FilterState } from '../../types';
interface TradeJournalFiltersProps {
    filters: FilterState;
    handleFilterChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;
    resetFilters: () => void;
    uniqueSetups: string[];
    uniqueModelTypes: string[];
    uniquePrimarySetupTypes: string[];
    uniqueSecondarySetupTypes: string[];
    uniqueLiquidityTypes: string[];
    uniqueDOLTypes: string[];
}
/**
 * Trade Journal Filters Component
 *
 * Simple wrapper that renders the F1FilterPanel.
 * Follows the proven architecture pattern.
 */
declare const TradeJournalFilters: React.FC<TradeJournalFiltersProps>;
export default TradeJournalFilters;
//# sourceMappingURL=TradeJournalFilters.d.ts.map