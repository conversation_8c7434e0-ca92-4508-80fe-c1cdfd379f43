/**
 * SelectDropdown Component
 *
 * A reusable dropdown component for selecting from a list of options.
 */
import React from "react";
interface SelectOption {
    value: string;
    label: string;
}
interface SelectDropdownProps {
    id: string;
    name: string;
    value: string;
    onChange: (e: React.ChangeEvent<HTMLSelectElement>) => void;
    options: SelectOption[];
    label?: string;
    required?: boolean;
    disabled?: boolean;
    className?: string;
    placeholder?: string;
}
/**
 * SelectDropdown Component
 *
 * A reusable dropdown component for selecting from a list of options.
 */
declare const SelectDropdown: React.FC<SelectDropdownProps>;
export default SelectDropdown;
//# sourceMappingURL=SelectDropdown.d.ts.map