/**
 * Filter Field Configuration
 *
 * REFACTORED FROM: TradeJournalFilters.tsx (321 lines → focused components)
 * Centralized configuration for all filter fields.
 *
 * BENEFITS:
 * - Single source of truth for filter definitions
 * - Easy to maintain and extend
 * - Type-safe filter configurations
 * - Eliminates repetitive filter field code
 * - Clear field organization
 */
import { FilterFieldType, FilterOption } from './F1FilterField';
export interface FilterFieldConfig {
    name: string;
    label: string;
    type: FilterFieldType;
    placeholder?: string;
    options?: FilterOption[];
    group: 'basic' | 'trading' | 'analysis' | 'dates';
    order: number;
}
/**
 * Complete filter field configuration
 */
export declare const FILTER_FIELD_CONFIG: FilterFieldConfig[];
/**
 * Get filter fields by group
 */
export declare const getFilterFieldsByGroup: (group: FilterFieldConfig["group"]) => FilterFieldConfig[];
/**
 * Get all filter groups
 */
export declare const getFilterGroups: () => FilterFieldConfig["group"][];
/**
 * Get filter field by name
 */
export declare const getFilterField: (name: string) => FilterFieldConfig | undefined;
/**
 * Update dynamic options for a field
 */
export declare const updateFieldOptions: (fieldName: string, options: FilterOption[]) => FilterFieldConfig[];
/**
 * Group labels for display
 */
export declare const FILTER_GROUP_LABELS: Record<FilterFieldConfig['group'], string>;
/**
 * Group descriptions
 */
export declare const FILTER_GROUP_DESCRIPTIONS: Record<FilterFieldConfig['group'], string>;
/**
 * Get field configuration with dynamic options
 */
export declare const getFieldConfigWithOptions: (uniqueData: {
    uniqueSetups: string[];
    uniqueModelTypes: string[];
    uniquePrimarySetupTypes: string[];
    uniqueSecondarySetupTypes: string[];
    uniqueLiquidityTypes: string[];
    uniqueDOLTypes: string[];
}) => FilterFieldConfig[];
export default FILTER_FIELD_CONFIG;
//# sourceMappingURL=filterFieldConfig.d.ts.map