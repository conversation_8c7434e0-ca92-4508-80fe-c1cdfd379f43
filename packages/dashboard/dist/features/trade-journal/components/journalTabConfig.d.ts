/**
 * Journal Tab Configuration
 *
 * REFACTORED FROM: TradeJournal.tsx (72 lines → focused components)
 * Centralized configuration for journal tabs and their content.
 *
 * BENEFITS:
 * - Single source of truth for tab definitions
 * - Easy to maintain and extend
 * - Type-safe tab configurations
 * - Reusable across different journal views
 * - Clear content mapping
 */
import React from 'react';
import { JournalTab } from './F1JournalTabs';
import { CompleteTradeData } from '@adhd-trading-dashboard/shared';
export interface JournalTabConfig {
    id: JournalTab;
    title: string;
    description: string;
    icon: string;
    component: React.ComponentType<any>;
    showInMobile: boolean;
    requiresData: boolean;
}
export interface JournalTabContentProps {
    /** Current active tab */
    activeTab: JournalTab;
    /** Journal data */
    data: {
        trades: CompleteTradeData[];
        filteredTrades: CompleteTradeData[];
        recentTrades: CompleteTradeData[];
        filters: any;
        uniqueSetups: string[];
        uniqueModelTypes: string[];
        uniquePrimarySetupTypes: string[];
        uniqueSecondarySetupTypes: string[];
        uniqueLiquidityTypes: string[];
        uniqueDOLTypes: string[];
    };
    /** Loading state */
    isLoading: boolean;
    /** Error state */
    error: string | null;
    /** Show filters state */
    showFilters: boolean;
    /** Action handlers */
    handlers: {
        handleFilterChange: (filters: any) => void;
        resetFilters: () => void;
        refreshTrades: () => void;
    };
}
/**
 * Tab configuration with components and metadata
 */
export declare const JOURNAL_TAB_CONFIG: Record<JournalTab, JournalTabConfig>;
/**
 * Get tab configuration by ID
 */
export declare const getTabConfig: (tabId: JournalTab) => JournalTabConfig;
/**
 * Get all tab configurations
 */
export declare const getAllTabConfigs: () => JournalTabConfig[];
/**
 * Get mobile-friendly tabs
 */
export declare const getMobileTabConfigs: () => JournalTabConfig[];
/**
 * Get tabs that require data
 */
export declare const getDataRequiredTabConfigs: () => JournalTabConfig[];
/**
 * Tab Content Renderer Component
 */
export declare const JournalTabContentRenderer: React.FC<JournalTabContentProps>;
export default JournalTabContentRenderer;
//# sourceMappingURL=journalTabConfig.d.ts.map