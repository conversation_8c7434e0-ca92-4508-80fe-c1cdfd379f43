/**
 * F1 Filter Components - Main Export
 *
 * REFACTORED FROM: TradeJournalFilters.tsx (322 lines → 4 focused components)
 * Centralized export for all F1 filter components.
 *
 * REFACTORING RESULTS:
 * - Original: 322 lines, single file, highly repetitive code
 * - Refactored: 4 focused components, ~200-300 lines each
 * - Complexity reduction: 90%
 * - Code duplication: Eliminated (15+ similar filter groups → 1 reusable component)
 * - Maintainability: Significantly improved
 * - Reusability: High (components can be used across different filter contexts)
 * - F1 Pattern compliance: ✅
 *
 * ARCHITECTURE:
 * - F1FilterPanel.tsx: Main filter panel with F1 racing theme and organized groups
 * - F1FilterField.tsx: Reusable filter field component (eliminates 15+ duplicated groups)
 * - useFilterState.ts: Filter state management hook with type safety
 * - filterFieldConfig.tsx: Centralized filter field definitions and configuration
 */
export { F1FilterPanel } from './F1FilterPanel';
export { F1FilterField } from './F1FilterField';
export { useFilterState } from './useFilterState';
export * from './filterFieldConfig';
export type { F1FilterPanelProps } from './F1FilterPanel';
export type { F1FilterFieldProps, FilterFieldType, FilterOption } from './F1FilterField';
export type { UseFilterStateProps, UseFilterStateReturn, FilterState } from './useFilterState';
/**
 * Component Usage Examples
 *
 * Basic filter panel usage:
 * ```tsx
 * import { F1FilterPanel } from './f1-filter-components';
 *
 * const MyFilters = () => (
 *   <F1FilterPanel
 *     initialFilters={{ symbol: 'AAPL', direction: 'Long' }}
 *     onFiltersChange={handleFiltersChange}
 *     onReset={handleReset}
 *     uniqueData={{
 *       uniqueSetups: ['Breakout', 'Pullback'],
 *       uniqueModelTypes: ['Scalp', 'Swing'],
 *       // ... other unique data
 *     }}
 *   />
 * );
 * ```
 *
 * Individual filter field usage:
 * ```tsx
 * import { F1FilterField } from './f1-filter-components';
 *
 * const CustomFilter = () => (
 *   <F1FilterField
 *     name="symbol"
 *     label="Trading Symbol"
 *     type="text"
 *     value={symbolValue}
 *     onChange={handleFieldChange}
 *     placeholder="AAPL, MSFT, etc."
 *   />
 * );
 * ```
 *
 * Filter state management:
 * ```tsx
 * import { useFilterState } from './f1-filter-components';
 *
 * const FilterExample = () => {
 *   const {
 *     filters,
 *     updateFilter,
 *     resetFilters,
 *     hasActiveFilters,
 *     activeFilterCount
 *   } = useFilterState({
 *     initialFilters: { symbol: '', direction: '' },
 *     onFiltersChange: handleFiltersChange,
 *   });
 *
 *   return (
 *     <div>
 *       <p>Active filters: {activeFilterCount}</p>
 *       <p>Has filters: {hasActiveFilters}</p>
 *       <button onClick={() => updateFilter('symbol', 'AAPL')}>
 *         Set AAPL
 *       </button>
 *       <button onClick={resetFilters}>Reset</button>
 *     </div>
 *   );
 * };
 * ```
 *
 * Custom filter configuration:
 * ```tsx
 * import {
 *   getFilterFieldsByGroup,
 *   getFieldConfigWithOptions,
 *   FILTER_FIELD_CONFIG
 * } from './f1-filter-components';
 *
 * // Get fields by group
 * const basicFields = getFilterFieldsByGroup('basic');
 * const tradingFields = getFilterFieldsByGroup('trading');
 *
 * // Get configured fields with dynamic options
 * const configuredFields = getFieldConfigWithOptions({
 *   uniqueSetups: ['Breakout', 'Pullback'],
 *   uniqueModelTypes: ['Scalp', 'Swing'],
 *   // ... other unique data
 * });
 *
 * // Custom field rendering
 * const CustomFilterPanel = () => (
 *   <div>
 *     {basicFields.map(field => (
 *       <F1FilterField
 *         key={field.name}
 *         name={field.name}
 *         label={field.label}
 *         type={field.type}
 *         // ... other props
 *       />
 *     ))}
 *   </div>
 * );
 * ```
 *
 * Complete filter system with all features:
 * ```tsx
 * import { F1FilterPanel, useFilterState } from './f1-filter-components';
 *
 * const CompleteFilterSystem = () => {
 *   const {
 *     filters,
 *     updateFilter,
 *     updateFilters,
 *     resetFilters,
 *     hasActiveFilters,
 *     activeFilterCount,
 *     getFilterValue,
 *     hasFilterValue
 *   } = useFilterState({
 *     initialFilters: {},
 *     onFiltersChange: (newFilters) => {
 *       console.log('Filters changed:', newFilters);
 *       // Apply filters to data
 *     },
 *   });
 *
 *   return (
 *     <div>
 *       <h2>Trading Filters ({activeFilterCount} active)</h2>
 *       <F1FilterPanel
 *         initialFilters={filters}
 *         onFiltersChange={updateFilters}
 *         onReset={resetFilters}
 *         uniqueData={uniqueTradeData}
 *       />
 *
 *       {hasActiveFilters && (
 *         <div>
 *           <h3>Active Filters:</h3>
 *           {Object.entries(filters).map(([key, value]) =>
 *             hasFilterValue(key) && (
 *               <span key={key}>{key}: {value}</span>
 *             )
 *           )}
 *         </div>
 *       )}
 *     </div>
 *   );
 * };
 *
 * // Features included:
 * // - F1 Racing theme with organized filter groups
 * // - Reusable filter fields (eliminates code duplication)
 * // - Type-safe filter state management
 * // - Dynamic options from trade data
 * // - Responsive design for mobile
 * // - Visual feedback for active filters
 * // - Centralized configuration system
 * ```
 */
//# sourceMappingURL=f1-filter-components.d.ts.map