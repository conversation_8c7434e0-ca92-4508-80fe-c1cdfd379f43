/**
 * F1FilterField Component
 *
 * REFACTORED FROM: TradeJournalFilters.tsx (321 lines → focused components)
 * Reusable filter field component with F1 racing theme.
 *
 * BENEFITS:
 * - Focused responsibility (single filter field)
 * - Eliminates code duplication (15+ similar filter groups)
 * - F1 racing theme with consistent styling
 * - Type-safe filter handling
 * - Accessible form controls
 */
import React from 'react';
export type FilterFieldType = 'text' | 'select' | 'date' | 'number';
export interface FilterOption {
    value: string | number;
    label: string;
}
export interface F1FilterFieldProps {
    /** Field identifier */
    name: string;
    /** Field label */
    label: string;
    /** Field type */
    type: FilterFieldType;
    /** Current value */
    value: string | number;
    /** Change handler */
    onChange: (name: string, value: string | number) => void;
    /** Options for select fields */
    options?: FilterOption[];
    /** Placeholder text */
    placeholder?: string;
    /** Whether field is disabled */
    disabled?: boolean;
    /** Custom className */
    className?: string;
}
/**
 * F1FilterField Component
 *
 * PATTERN: F1 Filter Field Pattern
 * - Racing-inspired styling with red accents
 * - Visual feedback for active filters
 * - Consistent form controls across field types
 * - Accessible with proper labels and focus
 * - Responsive design for mobile
 */
export declare const F1FilterField: React.FC<F1FilterFieldProps>;
export default F1FilterField;
//# sourceMappingURL=F1FilterField.d.ts.map