/**
 * useJournalNavigation Hook
 *
 * REFACTORED FROM: TradeJournal.tsx (72 lines → focused components)
 * Hook for managing journal navigation and tab state.
 *
 * BENEFITS:
 * - Focused responsibility (navigation only)
 * - Persistent tab state with localStorage
 * - Type-safe navigation handling
 * - Reusable across journal components
 * - Better separation of concerns
 */
import { JournalTab } from './F1JournalTabs';
export interface UseJournalNavigationProps {
    /** Default tab to show */
    defaultTab?: JournalTab;
    /** Storage key for persistence */
    storageKey?: string;
}
export interface UseJournalNavigationReturn {
    /** Current active tab */
    activeTab: JournalTab;
    /** Change active tab */
    setActiveTab: (tab: JournalTab) => void;
    /** Navigate to next tab */
    nextTab: () => void;
    /** Navigate to previous tab */
    previousTab: () => void;
    /** Check if tab is active */
    isTabActive: (tab: JournalTab) => boolean;
    /** Get tab index */
    getTabIndex: (tab: JournalTab) => number;
    /** Get all available tabs */
    availableTabs: JournalTab[];
    /** Show filters (for backward compatibility) */
    showFilters: boolean;
    /** Set show filters */
    setShowFilters: (show: boolean) => void;
}
/**
 * useJournalNavigation Hook
 *
 * Manages tab navigation state with persistence and keyboard navigation.
 */
export declare const useJournalNavigation: ({ defaultTab, storageKey, }?: UseJournalNavigationProps) => UseJournalNavigationReturn;
export default useJournalNavigation;
//# sourceMappingURL=useJournalNavigation.d.ts.map