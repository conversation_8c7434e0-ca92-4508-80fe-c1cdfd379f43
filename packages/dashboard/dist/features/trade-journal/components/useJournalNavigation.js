/**
 * useJournalNavigation Hook
 *
 * REFACTORED FROM: TradeJournal.tsx (72 lines → focused components)
 * Hook for managing journal navigation and tab state.
 *
 * BENEFITS:
 * - Focused responsibility (navigation only)
 * - Persistent tab state with localStorage
 * - Type-safe navigation handling
 * - Reusable across journal components
 * - Better separation of concerns
 */
import { useState, useCallback, useEffect } from 'react';
/**
 * Available tabs in order
 */
const AVAILABLE_TABS = ['all', 'recent', 'filters', 'stats'];
/**
 * Default storage key
 */
const DEFAULT_STORAGE_KEY = 'adhd-trading-dashboard:journal:active-tab';
/**
 * Load tab from localStorage
 */
const loadTabFromStorage = (storageKey, defaultTab) => {
    try {
        const stored = localStorage.getItem(storageKey);
        if (stored && AVAILABLE_TABS.includes(stored)) {
            return stored;
        }
    }
    catch (error) {
        console.warn('Failed to load journal tab from localStorage:', error);
    }
    return defaultTab;
};
/**
 * Save tab to localStorage
 */
const saveTabToStorage = (storageKey, tab) => {
    try {
        localStorage.setItem(storageKey, tab);
    }
    catch (error) {
        console.warn('Failed to save journal tab to localStorage:', error);
    }
};
/**
 * useJournalNavigation Hook
 *
 * Manages tab navigation state with persistence and keyboard navigation.
 */
export const useJournalNavigation = ({ defaultTab = 'all', storageKey = DEFAULT_STORAGE_KEY, } = {}) => {
    // Initialize active tab from storage or default
    const [activeTab, setActiveTabState] = useState(() => loadTabFromStorage(storageKey, defaultTab));
    // Backward compatibility: show filters state
    const [showFilters, setShowFilters] = useState(false);
    /**
     * Set active tab with persistence
     */
    const setActiveTab = useCallback((tab) => {
        if (AVAILABLE_TABS.includes(tab)) {
            setActiveTabState(tab);
            saveTabToStorage(storageKey, tab);
            // Auto-manage filters visibility based on tab
            if (tab === 'filters') {
                setShowFilters(true);
            }
        }
    }, [storageKey]);
    /**
     * Navigate to next tab
     */
    const nextTab = useCallback(() => {
        const currentIndex = AVAILABLE_TABS.indexOf(activeTab);
        const nextIndex = (currentIndex + 1) % AVAILABLE_TABS.length;
        setActiveTab(AVAILABLE_TABS[nextIndex]);
    }, [activeTab, setActiveTab]);
    /**
     * Navigate to previous tab
     */
    const previousTab = useCallback(() => {
        const currentIndex = AVAILABLE_TABS.indexOf(activeTab);
        const previousIndex = currentIndex === 0 ? AVAILABLE_TABS.length - 1 : currentIndex - 1;
        setActiveTab(AVAILABLE_TABS[previousIndex]);
    }, [activeTab, setActiveTab]);
    /**
     * Check if tab is active
     */
    const isTabActive = useCallback((tab) => {
        return activeTab === tab;
    }, [activeTab]);
    /**
     * Get tab index
     */
    const getTabIndex = useCallback((tab) => {
        return AVAILABLE_TABS.indexOf(tab);
    }, []);
    // Handle keyboard navigation
    useEffect(() => {
        const handleKeyDown = (event) => {
            // Only handle if no input is focused
            if (document.activeElement?.tagName === 'INPUT' ||
                document.activeElement?.tagName === 'TEXTAREA' ||
                document.activeElement?.tagName === 'SELECT') {
                return;
            }
            // Handle Ctrl/Cmd + Arrow keys for tab navigation
            if ((event.ctrlKey || event.metaKey) && !event.shiftKey) {
                switch (event.key) {
                    case 'ArrowLeft':
                        event.preventDefault();
                        previousTab();
                        break;
                    case 'ArrowRight':
                        event.preventDefault();
                        nextTab();
                        break;
                }
            }
            // Handle number keys for direct tab navigation
            if (event.key >= '1' && event.key <= '4' && !event.ctrlKey && !event.metaKey) {
                const tabIndex = parseInt(event.key) - 1;
                if (tabIndex < AVAILABLE_TABS.length) {
                    event.preventDefault();
                    setActiveTab(AVAILABLE_TABS[tabIndex]);
                }
            }
            // Handle Alt + Tab keys for journal-specific navigation
            if (event.altKey && !event.ctrlKey && !event.metaKey) {
                switch (event.key.toLowerCase()) {
                    case 'a':
                        event.preventDefault();
                        setActiveTab('all');
                        break;
                    case 'r':
                        event.preventDefault();
                        setActiveTab('recent');
                        break;
                    case 'f':
                        event.preventDefault();
                        setActiveTab('filters');
                        break;
                    case 's':
                        event.preventDefault();
                        setActiveTab('stats');
                        break;
                }
            }
            // Handle 'f' key to toggle filters (common UX pattern)
            if (event.key.toLowerCase() === 'f' && !event.ctrlKey && !event.metaKey && !event.altKey) {
                // Only if not in an input field
                if (document.activeElement?.tagName !== 'INPUT' &&
                    document.activeElement?.tagName !== 'TEXTAREA') {
                    event.preventDefault();
                    if (activeTab === 'filters') {
                        setShowFilters(!showFilters);
                    }
                    else {
                        setActiveTab('filters');
                    }
                }
            }
        };
        window.addEventListener('keydown', handleKeyDown);
        return () => window.removeEventListener('keydown', handleKeyDown);
    }, [nextTab, previousTab, setActiveTab, activeTab, showFilters]);
    return {
        activeTab,
        setActiveTab,
        nextTab,
        previousTab,
        isTabActive,
        getTabIndex,
        availableTabs: AVAILABLE_TABS,
        showFilters,
        setShowFilters,
    };
};
export default useJournalNavigation;
//# sourceMappingURL=useJournalNavigation.js.map