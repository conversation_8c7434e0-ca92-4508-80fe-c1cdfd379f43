/**
 * F1JournalTabs Component
 *
 * REFACTORED FROM: TradeJournal.tsx (72 lines → focused components)
 * F1 racing-themed tabs for journal navigation.
 *
 * BENEFITS:
 * - Focused responsibility (tab navigation only)
 * - F1 racing theme with smooth animations
 * - Consistent with other F1Tab components
 * - Better separation of concerns
 * - Reusable tab navigation pattern
 */
import React from 'react';
export type JournalTab = 'all' | 'recent' | 'filters' | 'stats';
export interface F1JournalTabsProps {
    /** Currently active tab */
    activeTab: JournalTab;
    /** Tab change handler */
    onTabChange: (tab: JournalTab) => void;
    /** Whether tabs are disabled */
    disabled?: boolean;
    /** Trade counts for badges */
    tradeCounts?: {
        total: number;
        recent: number;
        filtered: number;
    };
    /** Whether filters are active */
    hasActiveFilters?: boolean;
    /** Custom className */
    className?: string;
}
/**
 * F1JournalTabs Component
 *
 * PATTERN: F1 Tabs Pattern
 * - Racing-inspired styling with red accents
 * - Smooth hover animations and transitions
 * - Clear visual feedback for active state
 * - Accessible keyboard navigation
 * - Responsive design for mobile
 * - Trade count badges for context
 */
export declare const F1JournalTabs: React.FC<F1JournalTabsProps>;
export default F1JournalTabs;
//# sourceMappingURL=F1JournalTabs.d.ts.map