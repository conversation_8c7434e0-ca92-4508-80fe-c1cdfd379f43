/**
 * TimePicker Component
 *
 * A reusable time picker component for selecting hours and minutes.
 */
import React from "react";
interface TimePickerProps {
    id: string;
    name: string;
    value: string;
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
    label?: string;
    required?: boolean;
    disabled?: boolean;
    className?: string;
    placeholder?: string;
    min?: string;
    max?: string;
}
/**
 * TimePicker Component
 *
 * A reusable time picker component for selecting hours and minutes.
 */
declare const TimePicker: React.FC<TimePickerProps>;
export default TimePicker;
//# sourceMappingURL=TimePicker.d.ts.map