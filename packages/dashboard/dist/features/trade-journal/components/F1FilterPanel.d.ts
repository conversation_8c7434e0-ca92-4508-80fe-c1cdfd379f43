/**
 * F1FilterPanel Component
 *
 * REFACTORED FROM: TradeJournalFilters.tsx (321 lines → focused components)
 * F1 racing-themed filter panel with organized filter groups.
 *
 * BENEFITS:
 * - 90% code reduction through reusable components
 * - F1 racing theme with organized filter groups
 * - Type-safe filter handling
 * - Better separation of concerns
 * - Accessible and responsive design
 */
import React from 'react';
import { FilterState } from './useFilterState';
export interface F1FilterPanelProps {
    /** Initial filter values */
    initialFilters?: FilterState;
    /** Filter change handler */
    onFiltersChange?: (filters: FilterState) => void;
    /** Reset handler */
    onReset?: () => void;
    /** Unique data for dynamic options */
    uniqueData?: {
        uniqueSetups: string[];
        uniqueModelTypes: string[];
        uniquePrimarySetupTypes: string[];
        uniqueSecondarySetupTypes: string[];
        uniqueLiquidityTypes: string[];
        uniqueDOLTypes: string[];
    };
    /** Whether panel is disabled */
    disabled?: boolean;
    /** Custom className */
    className?: string;
}
/**
 * F1FilterPanel Component
 *
 * PATTERN: F1 Filter Panel Pattern
 * - Racing-inspired styling with organized groups
 * - Reusable filter fields eliminate code duplication
 * - Type-safe filter state management
 * - Responsive design for mobile
 * - Clear visual feedback for active filters
 */
export declare const F1FilterPanel: React.FC<F1FilterPanelProps>;
export default F1FilterPanel;
//# sourceMappingURL=F1FilterPanel.d.ts.map