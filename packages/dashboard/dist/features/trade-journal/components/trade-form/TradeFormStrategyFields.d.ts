/**
 * Trade Form Strategy Fields Component
 *
 * REFACTORED: Simplified strategy section focusing on core strategy elements.
 * Removed duplicate pattern quality field and legacy setup dropdown.
 * Now uses modern SetupBuilder for setup construction.
 */
import React from 'react';
import { TradeFormValues } from '../../types';
import { ValidationErrors } from '../../hooks/useTradeValidation';
interface TradeFormStrategyFieldsProps {
    formValues: TradeFormValues;
    handleChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => void;
    validationErrors: ValidationErrors;
    setFormValues: React.Dispatch<React.SetStateAction<TradeFormValues>>;
}
/**
 * Trade Form Strategy Fields Component
 */
declare const TradeFormStrategyFields: React.FC<TradeFormStrategyFieldsProps>;
export default TradeFormStrategyFields;
//# sourceMappingURL=TradeFormStrategyFields.d.ts.map