/**
 * Trade Form Field Configuration
 *
 * REFACTORED FROM: TradeFormBasicFields.tsx (338 lines → focused components)
 * Centralized configuration for all trading form fields.
 *
 * BENEFITS:
 * - Single source of truth for field definitions
 * - Easy to maintain and extend
 * - Consistent field options across forms
 * - Type-safe field configurations
 * - Reusable across different trading forms
 */
import { TradeFieldType, TradeFieldOption } from './F1TradeFormField';
export interface TradeFormFieldConfig {
    name: string;
    label: string;
    type: TradeFieldType;
    required?: boolean;
    options?: TradeFieldOption[];
    placeholder?: string;
    inputProps?: Record<string, any>;
    group: 'basic' | 'pricing' | 'strategy' | 'analysis';
}
/**
 * Direction Options
 */
export declare const DIRECTION_OPTIONS: TradeFieldOption[];
/**
 * Result Options
 */
export declare const RESULT_OPTIONS: TradeFieldOption[];
/**
 * Model Options
 */
export declare const MODEL_OPTIONS: TradeFieldOption[];
/**
 * Session Options
 */
export declare const SESSION_OPTIONS: TradeFieldOption[];
/**
 * RD Type Options
 */
export declare const RD_TYPE_OPTIONS: TradeFieldOption[];
/**
 * Draw on Liquidity Options
 */
export declare const DOL_STATUS_OPTIONS: TradeFieldOption[];
/**
 * Complete field configuration for trade forms
 */
export declare const TRADE_FORM_FIELDS: TradeFormFieldConfig[];
/**
 * Get fields by group
 */
export declare const getFieldsByGroup: (group: string) => TradeFormFieldConfig[];
/**
 * Get field configuration by name
 */
export declare const getFieldConfig: (name: string) => TradeFormFieldConfig | undefined;
/**
 * Field groups configuration
 */
export declare const FIELD_GROUPS: readonly [{
    readonly key: "basic";
    readonly title: "Basic Information";
    readonly description: "Essential trade details and identification";
    readonly icon: "📊";
}, {
    readonly key: "pricing";
    readonly title: "Pricing & P&L";
    readonly description: "Entry, exit prices and profit/loss calculations";
    readonly icon: "💰";
}, {
    readonly key: "strategy";
    readonly title: "Strategy & Setup";
    readonly description: "Trading model, session, and pattern quality";
    readonly icon: "🎯";
}, {
    readonly key: "analysis";
    readonly title: "Analysis & DOL";
    readonly description: "Draw on liquidity and advanced analysis";
    readonly icon: "🔍";
}];
/**
 * Validation rules for fields
 */
export declare const FIELD_VALIDATION_RULES: {
    readonly symbol: {
        readonly pattern: RegExp;
        readonly message: "Symbol must be 1-5 uppercase letters";
    };
    readonly entryPrice: {
        readonly min: 0.01;
        readonly message: "Entry price must be greater than 0";
    };
    readonly exitPrice: {
        readonly min: 0.01;
        readonly message: "Exit price must be greater than 0";
    };
    readonly quantity: {
        readonly min: 1;
        readonly message: "Quantity must be at least 1";
    };
    readonly patternQuality: {
        readonly min: 1;
        readonly max: 10;
        readonly message: "Pattern quality must be between 1 and 10";
    };
};
//# sourceMappingURL=tradeFormFieldConfig.d.ts.map