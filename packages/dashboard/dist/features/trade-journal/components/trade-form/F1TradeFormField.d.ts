/**
 * F1TradeFormField Component
 *
 * REFACTORED FROM: TradeFormBasicFields.tsx (338 lines → focused components)
 * Enhanced F1FormField specifically designed for trading forms.
 *
 * BENEFITS:
 * - Focused responsibility (single form field)
 * - F1 racing theme with trading-specific styling
 * - Built-in validation and error handling
 * - Supports all trading field types
 * - Reusable across all trading forms
 */
import React from 'react';
export type TradeFieldType = 'text' | 'number' | 'date' | 'select' | 'price' | 'quantity' | 'percentage';
export interface TradeFieldOption {
    value: string | number;
    label: string;
}
export interface F1TradeFormFieldProps {
    /** Field identifier */
    name: string;
    /** Field label */
    label: string;
    /** Field type */
    type: TradeFieldType;
    /** Current value */
    value: any;
    /** Change handler */
    onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;
    /** Options for select fields */
    options?: TradeFieldOption[];
    /** Input props for additional configuration */
    inputProps?: React.InputHTMLAttributes<HTMLInputElement>;
    /** Validation error */
    error?: string;
    /** Whether field is required */
    required?: boolean;
    /** Whether field is disabled */
    disabled?: boolean;
    /** Placeholder text */
    placeholder?: string;
    /** Custom className */
    className?: string;
}
/**
 * F1TradeFormField Component
 *
 * PATTERN: F1 Form Field Pattern for Trading
 * - Racing-inspired styling with red accents
 * - Trading-specific field types and formatting
 * - Built-in validation and error states
 * - Accessible with proper labels and focus
 * - Optimized for trading data entry
 */
export declare const F1TradeFormField: React.FC<F1TradeFormFieldProps>;
export default F1TradeFormField;
//# sourceMappingURL=F1TradeFormField.d.ts.map