/**
 * Trade Form Timing Fields Component
 *
 * REFACTORED: Consolidated timing sections into unified component.
 * Combines entry timing, session selection, and trade timing analysis
 * into a single cohesive section following compositional architecture.
 */
import React from 'react';
import { TradeFormValues } from '../../types';
import { ValidationErrors } from '../../hooks/useTradeValidation';
interface TradeFormTimingFieldsProps {
    formValues: TradeFormValues;
    handleChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => void;
    validationErrors: ValidationErrors;
}
/**
 * Trade Form Timing Fields Component
 */
declare const TradeFormTimingFields: React.FC<TradeFormTimingFieldsProps>;
export default TradeFormTimingFields;
//# sourceMappingURL=TradeFormTimingFields.d.ts.map