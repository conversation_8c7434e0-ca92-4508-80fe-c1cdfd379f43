/**
 * useTradeFormFields Hook
 *
 * REFACTORED FROM: TradeFormBasicFields.tsx (338 lines → focused components)
 * Enhanced hook for managing trade form fields with validation and calculations.
 *
 * BENEFITS:
 * - Focused responsibility (field management only)
 * - Built-in validation with field-specific rules
 * - Automatic profit/loss calculations
 * - Type-safe field handling
 * - Reusable across different trading forms
 */
import { useCallback } from 'react';
import { FIELD_VALIDATION_RULES, getFieldConfig } from './tradeFormFieldConfig';
/**
 * Fields that trigger profit/loss calculations
 */
const CALCULATION_FIELDS = ['entryPrice', 'exitPrice', 'quantity', 'direction'];
/**
 * useTradeFormFields Hook
 *
 * Enhanced form field management with validation and calculations.
 */
export const useTradeFormFields = ({
  formValues,
  setFormValues,
  // validationErrors, // Removed as unused
  setValidationErrors,
  calculateProfitLoss,
}) => {
  /**
   * Validate individual field
   */
  const validateField = useCallback((name, value) => {
    const fieldConfig = getFieldConfig(name);
    const validationRule = FIELD_VALIDATION_RULES[name];
    // Check required fields
    if (fieldConfig?.required && (!value || value === '')) {
      return `${fieldConfig.label} is required`;
    }
    // Skip validation for empty optional fields
    if (!value || value === '') {
      return null;
    }
    // Apply field-specific validation rules
    if (validationRule) {
      if ('pattern' in validationRule) {
        if (!validationRule.pattern.test(value)) {
          return validationRule.message;
        }
      }
      if ('min' in validationRule) {
        const numValue = parseFloat(value);
        if (isNaN(numValue) || numValue < validationRule.min) {
          return validationRule.message;
        }
      }
      if ('max' in validationRule) {
        const numValue = parseFloat(value);
        if (isNaN(numValue) || numValue > validationRule.max) {
          return validationRule.message;
        }
      }
    }
    // Additional validation for specific field types
    switch (name) {
      case 'date':
        const date = new Date(value);
        if (isNaN(date.getTime())) {
          return 'Please enter a valid date';
        }
        if (date > new Date()) {
          return 'Date cannot be in the future';
        }
        break;
      case 'entryPrice':
      case 'exitPrice':
      case 'quantity':
      case 'profit':
        const numValue = parseFloat(value);
        if (isNaN(numValue)) {
          return 'Please enter a valid number';
        }
        break;
    }
    return null;
  }, []);
  /**
   * Standard change handler
   */
  const handleChange = useCallback(
    e => {
      const { name, value } = e.target;
      // Update form values
      setFormValues(prev => ({ ...prev, [name]: value }));
      // Validate field and update errors
      const fieldError = validateField(name, value);
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        if (fieldError) {
          newErrors[name] = fieldError;
        } else {
          delete newErrors[name];
        }
        return newErrors;
      });
    },
    [validateField, setFormValues, setValidationErrors]
  );
  /**
   * Calculate profit/loss from current form values
   */
  const calculateProfitFromValues = useCallback(() => {
    const entryPrice = parseFloat(formValues.entryPrice) || 0;
    const exitPrice = parseFloat(formValues.exitPrice) || 0;
    const quantity = parseFloat(formValues.quantity) || 0;
    const direction = formValues.direction;
    if (entryPrice === 0 || exitPrice === 0 || quantity === 0) {
      return 0;
    }
    const priceDiff = direction === 'long' ? exitPrice - entryPrice : entryPrice - exitPrice;
    return priceDiff * quantity;
  }, [formValues.entryPrice, formValues.exitPrice, formValues.quantity, formValues.direction]);
  /**
   * Price change handler (triggers calculations)
   */
  const handlePriceChange = useCallback(
    e => {
      const { name, value } = e.target;
      // Update form values
      setFormValues(prev => {
        const newValues = { ...prev, [name]: value };
        // Auto-calculate profit if all required fields are present
        if (CALCULATION_FIELDS.every(field => (field === name ? value : newValues[field]))) {
          const entryPrice = parseFloat(name === 'entryPrice' ? value : newValues.entryPrice) || 0;
          const exitPrice = parseFloat(name === 'exitPrice' ? value : newValues.exitPrice) || 0;
          const quantity = parseFloat(name === 'quantity' ? value : newValues.quantity) || 0;
          const direction = name === 'direction' ? value : newValues.direction;
          if (entryPrice > 0 && exitPrice > 0 && quantity > 0) {
            const priceDiff =
              direction === 'long' ? exitPrice - entryPrice : entryPrice - exitPrice;
            newValues.profit = (priceDiff * quantity).toFixed(2);
          }
        }
        return newValues;
      });
      // Validate field
      const fieldError = validateField(name, value);
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        if (fieldError) {
          newErrors[name] = fieldError;
        } else {
          delete newErrors[name];
        }
        return newErrors;
      });
      // Trigger external calculation if provided
      if (calculateProfitLoss) {
        setTimeout(calculateProfitLoss, 0);
      }
    },
    [validateField, setFormValues, setValidationErrors, calculateProfitLoss]
  );
  /**
   * Validate all fields
   */
  const validateAllFields = useCallback(() => {
    const errors = {};
    let isValid = true;
    // Validate all form values
    Object.entries(formValues).forEach(([name, value]) => {
      const error = validateField(name, value);
      if (error) {
        errors[name] = error;
        isValid = false;
      }
    });
    setValidationErrors(errors);
    return isValid;
  }, [formValues, validateField, setValidationErrors]);
  /**
   * Check if field affects calculations
   */
  const isCalculationField = useCallback(fieldName => {
    return CALCULATION_FIELDS.includes(fieldName);
  }, []);
  return {
    handleChange,
    handlePriceChange,
    validateField,
    validateAllFields,
    calculateProfitFromValues,
    isCalculationField,
  };
};
export default useTradeFormFields;
//# sourceMappingURL=useTradeFormFields.js.map
