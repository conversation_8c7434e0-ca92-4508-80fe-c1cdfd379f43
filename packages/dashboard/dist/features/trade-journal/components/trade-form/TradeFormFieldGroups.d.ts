/**
 * TradeFormFieldGroups Component
 *
 * REFACTORED FROM: TradeFormBasicFields.tsx (338 lines → focused components)
 * Organized field groups with F1 racing theme and clear hierarchy.
 *
 * BENEFITS:
 * - Organized field groups with clear sections
 * - F1 racing theme with consistent styling
 * - Responsive grid layout
 * - Reusable across different trading forms
 * - Better UX with logical field grouping
 */
import React from 'react';
import { TradeFormValues } from '../../types';
import { ValidationErrors } from '../../hooks/useTradeValidation';
export interface TradeFormFieldGroupsProps {
    /** Form values */
    formValues: TradeFormValues;
    /** Change handler */
    handleChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;
    /** Price change handler (triggers calculations) */
    handlePriceChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
    /** Validation errors */
    validationErrors: ValidationErrors;
    /** Whether form is disabled */
    disabled?: boolean;
    /** Custom className */
    className?: string;
}
/**
 * TradeFormFieldGroups Component
 *
 * PATTERN: F1 Form Groups Pattern
 * - Racing-inspired section styling with icons
 * - Organized field groups with clear hierarchy
 * - Responsive grid layout for optimal UX
 * - Consistent F1 theme across all groups
 * - Accessible and keyboard navigable
 */
export declare const TradeFormFieldGroups: React.FC<TradeFormFieldGroupsProps>;
export default TradeFormFieldGroups;
//# sourceMappingURL=TradeFormFieldGroups.d.ts.map