{"version": 3, "file": "tradeFormFieldConfig.js", "sourceRoot": "", "sources": ["../../../../../src/features/trade-journal/components/trade-form/tradeFormFieldConfig.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;GAYG;AAeH;;GAEG;AACH,MAAM,CAAC,MAAM,iBAAiB,GAAuB;IACnD,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE;IAChC,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE;CACnC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,cAAc,GAAuB;IAChD,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE;IAC9B,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE;IAChC,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,WAAW,EAAE;CAC3C,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,aAAa,GAAuB;IAC/C,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,SAAS,EAAE;IACtC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE;IACpC,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,UAAU,EAAE;CACzC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,eAAe,GAAuB;IACjD,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,SAAS,EAAE;IACtC,EAAE,KAAK,EAAE,aAAa,EAAE,KAAK,EAAE,aAAa,EAAE;IAC9C,EAAE,KAAK,EAAE,aAAa,EAAE,KAAK,EAAE,aAAa,EAAE;IAC9C,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE;IAC9B,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,WAAW,EAAE;CAC3C,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,eAAe,GAAuB;IACjD,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,SAAS,EAAE;IACtC,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,SAAS,EAAE;IACtC,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,SAAS,EAAE;CACvC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,kBAAkB,GAAuB;IACpD,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE;IAC9B,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE;IACpC,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,SAAS,EAAE;IACtC,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,SAAS,EAAE;CACvC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,iBAAiB,GAA2B;IACvD,2BAA2B;IAC3B;QACE,IAAI,EAAE,MAAM;QACZ,KAAK,EAAE,MAAM;QACb,IAAI,EAAE,MAAM;QACZ,QAAQ,EAAE,IAAI;QACd,KAAK,EAAE,OAAO;KACf;IACD;QACE,IAAI,EAAE,QAAQ;QACd,KAAK,EAAE,QAAQ;QACf,IAAI,EAAE,MAAM;QACZ,QAAQ,EAAE,IAAI;QACd,WAAW,EAAE,qBAAqB;QAClC,KAAK,EAAE,OAAO;QACd,UAAU,EAAE;YACV,KAAK,EAAE,EAAE,aAAa,EAAE,WAAW,EAAE;SACtC;KACF;IACD;QACE,IAAI,EAAE,WAAW;QACjB,KAAK,EAAE,WAAW;QAClB,IAAI,EAAE,QAAQ;QACd,QAAQ,EAAE,IAAI;QACd,OAAO,EAAE,iBAAiB;QAC1B,KAAK,EAAE,OAAO;KACf;IACD;QACE,IAAI,EAAE,QAAQ;QACd,KAAK,EAAE,QAAQ;QACf,IAAI,EAAE,QAAQ;QACd,QAAQ,EAAE,IAAI;QACd,OAAO,EAAE,cAAc;QACvB,KAAK,EAAE,OAAO;KACf;IAED,iBAAiB;IACjB;QACE,IAAI,EAAE,YAAY;QAClB,KAAK,EAAE,aAAa;QACpB,IAAI,EAAE,OAAO;QACb,QAAQ,EAAE,IAAI;QACd,KAAK,EAAE,SAAS;KACjB;IACD;QACE,IAAI,EAAE,WAAW;QACjB,KAAK,EAAE,YAAY;QACnB,IAAI,EAAE,OAAO;QACb,QAAQ,EAAE,IAAI;QACd,KAAK,EAAE,SAAS;KACjB;IACD;QACE,IAAI,EAAE,UAAU;QAChB,KAAK,EAAE,UAAU;QACjB,IAAI,EAAE,UAAU;QAChB,QAAQ,EAAE,IAAI;QACd,KAAK,EAAE,SAAS;KACjB;IACD;QACE,IAAI,EAAE,QAAQ;QACd,KAAK,EAAE,iBAAiB;QACxB,IAAI,EAAE,OAAO;QACb,QAAQ,EAAE,IAAI;QACd,KAAK,EAAE,SAAS;KACjB;IAED,kBAAkB;IAClB;QACE,IAAI,EAAE,OAAO;QACb,KAAK,EAAE,OAAO;QACd,IAAI,EAAE,QAAQ;QACd,OAAO,EAAE,aAAa;QACtB,KAAK,EAAE,UAAU;KAClB;IACD;QACE,IAAI,EAAE,SAAS;QACf,KAAK,EAAE,SAAS;QAChB,IAAI,EAAE,QAAQ;QACd,OAAO,EAAE,eAAe;QACxB,KAAK,EAAE,UAAU;KAClB;IACD;QACE,IAAI,EAAE,gBAAgB;QACtB,KAAK,EAAE,wBAAwB;QAC/B,IAAI,EAAE,QAAQ;QACd,KAAK,EAAE,UAAU;QACjB,UAAU,EAAE;YACV,GAAG,EAAE,CAAC;YACN,GAAG,EAAE,EAAE;YACP,IAAI,EAAE,CAAC;SACR;KACF;IAED,kBAAkB;IAClB;QACE,IAAI,EAAE,WAAW;QACjB,KAAK,EAAE,YAAY;QACnB,IAAI,EAAE,MAAM;QACZ,WAAW,EAAE,0BAA0B;QACvC,KAAK,EAAE,UAAU;KAClB;IACD;QACE,IAAI,EAAE,QAAQ;QACd,KAAK,EAAE,SAAS;QAChB,IAAI,EAAE,QAAQ;QACd,OAAO,EAAE,eAAe;QACxB,KAAK,EAAE,UAAU;KAClB;IACD;QACE,IAAI,EAAE,iBAAiB;QACvB,KAAK,EAAE,mBAAmB;QAC1B,IAAI,EAAE,QAAQ;QACd,OAAO,EAAE,kBAAkB;QAC3B,KAAK,EAAE,UAAU;KAClB;IACD;QACE,IAAI,EAAE,cAAc;QACpB,KAAK,EAAE,eAAe;QACtB,IAAI,EAAE,MAAM;QACZ,WAAW,EAAE,yBAAyB;QACtC,KAAK,EAAE,UAAU;KAClB;CACF,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,gBAAgB,GAAG,CAAC,KAAa,EAA0B,EAAE;IACxE,OAAO,iBAAiB,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,KAAK,KAAK,CAAC,CAAC;AAClE,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,cAAc,GAAG,CAAC,IAAY,EAAoC,EAAE;IAC/E,OAAO,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC;AAC9D,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,YAAY,GAAG;IAC1B;QACE,GAAG,EAAE,OAAO;QACZ,KAAK,EAAE,mBAAmB;QAC1B,WAAW,EAAE,4CAA4C;QACzD,IAAI,EAAE,IAAI;KACX;IACD;QACE,GAAG,EAAE,SAAS;QACd,KAAK,EAAE,eAAe;QACtB,WAAW,EAAE,iDAAiD;QAC9D,IAAI,EAAE,IAAI;KACX;IACD;QACE,GAAG,EAAE,UAAU;QACf,KAAK,EAAE,kBAAkB;QACzB,WAAW,EAAE,6CAA6C;QAC1D,IAAI,EAAE,IAAI;KACX;IACD;QACE,GAAG,EAAE,UAAU;QACf,KAAK,EAAE,gBAAgB;QACvB,WAAW,EAAE,yCAAyC;QACtD,IAAI,EAAE,IAAI;KACX;CACO,CAAC;AAEX;;GAEG;AACH,MAAM,CAAC,MAAM,sBAAsB,GAAG;IACpC,MAAM,EAAE;QACN,OAAO,EAAE,cAAc;QACvB,OAAO,EAAE,sCAAsC;KAChD;IACD,UAAU,EAAE;QACV,GAAG,EAAE,IAAI;QACT,OAAO,EAAE,oCAAoC;KAC9C;IACD,SAAS,EAAE;QACT,GAAG,EAAE,IAAI;QACT,OAAO,EAAE,mCAAmC;KAC7C;IACD,QAAQ,EAAE;QACR,GAAG,EAAE,CAAC;QACN,OAAO,EAAE,6BAA6B;KACvC;IACD,cAAc,EAAE;QACd,GAAG,EAAE,CAAC;QACN,GAAG,EAAE,EAAE;QACP,OAAO,EAAE,0CAA0C;KACpD;CACO,CAAC"}