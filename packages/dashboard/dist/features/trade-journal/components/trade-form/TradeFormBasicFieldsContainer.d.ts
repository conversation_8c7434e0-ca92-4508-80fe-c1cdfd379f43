/**
 * TradeFormBasicFieldsContainer Component
 *
 * REFACTORED FROM: TradeFormBasicFields.tsx (338 lines → focused components)
 * Main orchestrator for trade form basic fields with F1 container pattern.
 *
 * BENEFITS:
 * - Uses F1Container for consistent styling
 * - Separates orchestration from presentation
 * - Better error handling and validation
 * - Follows proven container pattern
 * - F1 racing theme integration
 */
import React from 'react';
import { TradeFormValues } from '../../types';
import { ValidationErrors } from '../../hooks/useTradeValidation';
export interface TradeFormBasicFieldsContainerProps {
    /** Form values */
    formValues: TradeFormValues;
    /** Form values setter */
    setFormValues?: React.Dispatch<React.SetStateAction<TradeFormValues>>;
    /** Change handler */
    handleChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => void;
    /** Validation errors */
    validationErrors: ValidationErrors;
    /** Validation errors setter */
    setValidationErrors?: React.Dispatch<React.SetStateAction<ValidationErrors>>;
    /** Calculate profit/loss callback */
    calculateProfitLoss?: () => void;
    /** Whether form is disabled */
    disabled?: boolean;
    /** Custom className */
    className?: string;
}
/**
 * TradeFormBasicFieldsContainer Component
 *
 * PATTERN: F1 Container Pattern
 * - Error boundaries and loading states
 * - Consistent F1 styling and theme
 * - Proper separation of concerns
 * - Suspense for code splitting
 */
export declare const TradeFormBasicFieldsContainer: React.FC<TradeFormBasicFieldsContainerProps>;
export default TradeFormBasicFieldsContainer;
//# sourceMappingURL=TradeFormBasicFieldsContainer.d.ts.map