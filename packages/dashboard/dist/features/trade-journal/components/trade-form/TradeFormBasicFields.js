import { jsx as _jsx } from "react/jsx-runtime";
import { TradeFormBasicFieldsContainer } from './TradeFormBasicFieldsContainer';
/**
 * Trade Form Basic Fields Component
 *
 * Simple wrapper that renders the container.
 * Follows the proven architecture pattern.
 */
const TradeFormBasicFields = (props) => {
    return _jsx(TradeFormBasicFieldsContainer, { ...props });
};
export default TradeFormBasicFields;
//# sourceMappingURL=TradeFormBasicFields.js.map