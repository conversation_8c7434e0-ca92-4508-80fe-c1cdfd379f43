/**
 * F1JournalContainer Component
 *
 * REFACTORED FROM: TradeJournal.tsx (72 lines → focused components)
 * Main orchestrator for trade journal with F1 container pattern.
 *
 * BENEFITS:
 * - Uses F1Container for consistent styling
 * - Separates orchestration from presentation
 * - Better error handling and loading states
 * - Follows proven container pattern
 * - F1 racing theme integration
 */
import React from 'react';
export interface F1JournalContainerProps {
    /** Custom className */
    className?: string;
    /** Initial tab to display */
    initialTab?: 'all' | 'recent' | 'filters' | 'stats';
}
/**
 * F1JournalContainer Component
 *
 * PATTERN: F1 Container Pattern
 * - Error boundaries and loading states
 * - Consistent F1 styling and theme
 * - Proper separation of concerns
 * - Suspense for code splitting
 */
export declare const F1JournalContainer: React.FC<F1JournalContainerProps>;
export default F1JournalContainer;
//# sourceMappingURL=F1JournalContainer.d.ts.map