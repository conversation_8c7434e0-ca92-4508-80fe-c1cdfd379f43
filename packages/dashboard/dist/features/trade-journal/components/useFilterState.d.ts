/**
 * useFilterState Hook
 *
 * REFACTORED FROM: TradeJournalFilters.tsx (321 lines → focused components)
 * Hook for managing filter state and operations.
 *
 * BENEFITS:
 * - Focused responsibility (filter state only)
 * - Type-safe filter handling
 * - Reusable across filter components
 * - Better separation of concerns
 * - Centralized filter logic
 */
export interface FilterState {
    [key: string]: string | number;
}
export interface UseFilterStateProps {
    /** Initial filter values */
    initialFilters?: FilterState;
    /** Filter change handler */
    onFiltersChange?: (filters: FilterState) => void;
    /** Reset handler */
    onReset?: () => void;
}
export interface UseFilterStateReturn {
    /** Current filter values */
    filters: FilterState;
    /** Update a single filter */
    updateFilter: (name: string, value: string | number) => void;
    /** Update multiple filters */
    updateFilters: (newFilters: Partial<FilterState>) => void;
    /** Reset all filters */
    resetFilters: () => void;
    /** Check if any filters are active */
    hasActiveFilters: boolean;
    /** Get active filter count */
    activeFilterCount: number;
    /** Get filter value */
    getFilterValue: (name: string) => string | number;
    /** Check if filter has value */
    hasFilterValue: (name: string) => boolean;
}
/**
 * useFilterState Hook
 *
 * Manages filter state with type safety and utility functions.
 */
export declare const useFilterState: ({ initialFilters, onFiltersChange, onReset, }?: UseFilterStateProps) => UseFilterStateReturn;
export default useFilterState;
//# sourceMappingURL=useFilterState.d.ts.map