/**
 * useFilterState Hook
 *
 * REFACTORED FROM: TradeJournalFilters.tsx (321 lines → focused components)
 * Hook for managing filter state and operations.
 *
 * BENEFITS:
 * - Focused responsibility (filter state only)
 * - Type-safe filter handling
 * - Reusable across filter components
 * - Better separation of concerns
 * - Centralized filter logic
 */
import { useState, useCallback, useMemo } from 'react';
/**
 * useFilterState Hook
 *
 * Manages filter state with type safety and utility functions.
 */
export const useFilterState = ({ initialFilters = {}, onFiltersChange, onReset } = {}) => {
  const [filters, setFilters] = useState(initialFilters);
  /**
   * Update a single filter
   */
  const updateFilter = useCallback(
    (name, value) => {
      setFilters(prev => {
        const newFilters = {
          ...prev,
          [name]: value,
        };
        // Call external handler if provided
        if (onFiltersChange) {
          onFiltersChange(newFilters);
        }
        return newFilters;
      });
    },
    [onFiltersChange]
  );
  /**
   * Update multiple filters
   */
  const updateFilters = useCallback(
    newFilters => {
      setFilters(prev => {
        const updatedFilters = {
          ...prev,
          ...Object.fromEntries(
            Object.entries(newFilters)
              .filter(([, value]) => value !== undefined)
              .map(([key, value]) => [key, value])
          ),
        };
        // Call external handler if provided
        if (onFiltersChange) {
          onFiltersChange(updatedFilters);
        }
        return updatedFilters;
      });
    },
    [onFiltersChange]
  );
  /**
   * Reset all filters
   */
  const resetFilters = useCallback(() => {
    setFilters(initialFilters);
    // Call external handlers if provided
    if (onReset) {
      onReset();
    }
    if (onFiltersChange) {
      onFiltersChange(initialFilters);
    }
  }, [initialFilters, onReset, onFiltersChange]);
  /**
   * Check if any filters are active
   */
  const hasActiveFilters = useMemo(() => {
    return Object.values(filters).some(
      value => value !== '' && value !== null && value !== undefined
    );
  }, [filters]);
  /**
   * Get active filter count
   */
  const activeFilterCount = useMemo(() => {
    return Object.values(filters).filter(
      value => value !== '' && value !== null && value !== undefined
    ).length;
  }, [filters]);
  /**
   * Get filter value
   */
  const getFilterValue = useCallback(
    name => {
      return filters[name] || '';
    },
    [filters]
  );
  /**
   * Check if filter has value
   */
  const hasFilterValue = useCallback(
    name => {
      const value = filters[name];
      return value !== '' && value !== null && value !== undefined;
    },
    [filters]
  );
  return {
    filters,
    updateFilter,
    updateFilters,
    resetFilters,
    hasActiveFilters,
    activeFilterCount,
    getFilterValue,
    hasFilterValue,
  };
};
export default useFilterState;
//# sourceMappingURL=useFilterState.js.map
