/**
 * Trade Analysis Section Component
 *
 * Unified component that consolidates Pattern Quality Assessment and DOL Analysis
 * into a single cohesive section following the DashboardSection pattern.
 *
 * ARCHITECTURE:
 * - Follows compositional design with focused sub-components
 * - Uses DashboardSection wrapper for consistent styling
 * - Maintains TypeScript strict typing
 * - F1 racing theme integration
 */
import React from 'react';
import { TradeFormValues } from '../../types';
import { ValidationErrors } from '../../hooks/useTradeValidation';
export interface TradeAnalysisSectionProps {
    /** Form values containing analysis data */
    formValues: TradeFormValues;
    /** Change handler for form updates */
    onChange: (field: string, value: any) => void;
    /** Validation errors */
    validationErrors: ValidationErrors;
    /** Whether the section is disabled */
    disabled?: boolean;
    /** Custom className */
    className?: string;
}
/**
 * Trade Analysis Section Component
 *
 * Consolidates Pattern Quality Assessment and DOL Analysis into a unified section.
 * Follows the established DashboardSection pattern for consistency.
 */
export declare const TradeAnalysisSection: React.FC<TradeAnalysisSectionProps>;
export default TradeAnalysisSection;
//# sourceMappingURL=TradeAnalysisSection.d.ts.map