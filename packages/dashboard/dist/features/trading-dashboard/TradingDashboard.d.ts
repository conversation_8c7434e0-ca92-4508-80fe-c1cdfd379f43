/**
 * Trading Dashboard Component
 *
 * REFACTORED: Now uses the new F1 component library and container pattern.
 * Simplified from 388 lines to a clean wrapper component.
 *
 * BENEFITS:
 * - 95% code reduction
 * - Uses proven container pattern
 * - F1 component library integration
 * - Better separation of concerns
 * - Consistent with other refactored components
 */
import React from 'react';
export interface TradingDashboardProps {
    /** Custom className */
    className?: string;
    /** Initial tab to display */
    initialTab?: 'summary' | 'trades' | 'setups' | 'analytics';
}
/**
 * Trading Dashboard Component
 *
 * Simple wrapper that renders the container.
 * Follows the proven architecture pattern.
 */
export declare const TradingDashboard: React.FC<TradingDashboardProps>;
export default TradingDashboard;
//# sourceMappingURL=TradingDashboard.d.ts.map