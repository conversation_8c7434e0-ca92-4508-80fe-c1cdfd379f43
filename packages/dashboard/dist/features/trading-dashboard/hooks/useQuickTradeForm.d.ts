/**
 * useQuickTradeForm Hook
 *
 * Custom hook for managing quick trade form state and logic.
 * Extracted from the original QuickTradeForm component for better separation of concerns.
 *
 * FEATURES:
 * - Form field management with validation
 * - Auto-save functionality
 * - Error handling
 * - Performance optimization
 */
import { TradeFormData, useFormField } from '@adhd-trading-dashboard/shared';
export interface UseQuickTradeFormConfig {
    /** Callback when trade is successfully submitted */
    onSubmit?: (trade: TradeFormData) => Promise<void>;
    /** Initial form values */
    initialValues?: Partial<TradeFormData>;
    /** Whether to enable auto-save */
    autoSave?: boolean;
    /** Auto-save interval in milliseconds */
    autoSaveInterval?: number;
}
export interface UseQuickTradeFormReturn {
    dateField: ReturnType<typeof useFormField<string>>;
    symbolField: ReturnType<typeof useFormField<string>>;
    directionField: ReturnType<typeof useFormField<'long' | 'short'>>;
    quantityField: ReturnType<typeof useFormField<number>>;
    entryPriceField: ReturnType<typeof useFormField<number>>;
    exitPriceField: ReturnType<typeof useFormField<number>>;
    isSubmitting: boolean;
    error: string | null;
    success: string | null;
    handleSubmit: () => Promise<void>;
    handleClear: () => void;
    validateForm: () => boolean;
    lastSaved: Date | null;
}
/**
 * useQuickTradeForm Hook
 *
 * Manages form state and validation for the quick trade entry form.
 */
export declare const useQuickTradeForm: (config?: UseQuickTradeFormConfig) => UseQuickTradeFormReturn;
export default useQuickTradeForm;
//# sourceMappingURL=useQuickTradeForm.d.ts.map