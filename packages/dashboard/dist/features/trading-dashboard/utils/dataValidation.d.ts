/**
 * Data Validation Utilities
 *
 * EXTRACTED FROM: Data transformation logic for better error handling
 * Provides data validation, sanitization, and fallback mechanisms.
 *
 * BENEFITS:
 * - Prevents crashes from invalid data
 * - Provides meaningful error messages
 * - Ensures data consistency
 * - Supports graceful degradation
 */
/**
 * Validation error types
 */
export interface ValidationError {
    field: string;
    value: any;
    message: string;
    severity: 'error' | 'warning' | 'info';
}
export interface ValidationResult {
    isValid: boolean;
    errors: ValidationError[];
    warnings: ValidationError[];
    sanitizedData?: any;
}
/**
 * Data validation utilities
 */
export declare const DataValidators: {
    /**
     * Validate a single trade object
     */
    validateTrade: (trade: any, index?: number) => ValidationResult;
    /**
     * Validate an array of trades
     */
    validateTrades: (trades: any[]) => ValidationResult;
    /**
     * Validate CompleteTradeData structure
     */
    validateCompleteTradeData: (data: any[]) => ValidationResult;
};
/**
 * Data sanitization utilities
 */
export declare const DataSanitizers: {
    /**
     * Sanitize a trade object with fallback values
     */
    sanitizeTrade: (trade: any, index?: number) => any;
    /**
     * Sanitize date string
     */
    sanitizeDate: (date: any) => string | null;
    /**
     * Sanitize time string
     */
    sanitizeTime: (time: any) => string | null;
    /**
     * Sanitize direction value
     */
    sanitizeDirection: (direction: any) => "Long" | "Short" | null;
    /**
     * Sanitize numeric value
     */
    sanitizeNumber: (value: any) => number | null;
};
/**
 * Error reporting utilities
 */
export declare const ErrorReporter: {
    /**
     * Log validation results
     */
    logValidationResults: (result: ValidationResult, context?: string) => void;
    /**
     * Create user-friendly error message
     */
    createErrorMessage: (result: ValidationResult) => string;
};
declare const _default: {
    DataValidators: {
        /**
         * Validate a single trade object
         */
        validateTrade: (trade: any, index?: number) => ValidationResult;
        /**
         * Validate an array of trades
         */
        validateTrades: (trades: any[]) => ValidationResult;
        /**
         * Validate CompleteTradeData structure
         */
        validateCompleteTradeData: (data: any[]) => ValidationResult;
    };
    DataSanitizers: {
        /**
         * Sanitize a trade object with fallback values
         */
        sanitizeTrade: (trade: any, index?: number) => any;
        /**
         * Sanitize date string
         */
        sanitizeDate: (date: any) => string | null;
        /**
         * Sanitize time string
         */
        sanitizeTime: (time: any) => string | null;
        /**
         * Sanitize direction value
         */
        sanitizeDirection: (direction: any) => "Long" | "Short" | null;
        /**
         * Sanitize numeric value
         */
        sanitizeNumber: (value: any) => number | null;
    };
    ErrorReporter: {
        /**
         * Log validation results
         */
        logValidationResults: (result: ValidationResult, context?: string) => void;
        /**
         * Create user-friendly error message
         */
        createErrorMessage: (result: ValidationResult) => string;
    };
};
export default _default;
//# sourceMappingURL=dataValidation.d.ts.map