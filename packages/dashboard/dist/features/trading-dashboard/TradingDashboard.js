import { jsx as _jsx } from "react/jsx-runtime";
import { F1DashboardContainer } from './components/F1DashboardContainer';
/**
 * Trading Dashboard Component
 *
 * Simple wrapper that renders the container.
 * Follows the proven architecture pattern.
 */
export const TradingDashboard = ({ className, initialTab }) => {
    return _jsx(F1DashboardContainer, { className: className, initialTab: initialTab });
};
export default TradingDashboard;
//# sourceMappingURL=TradingDashboard.js.map