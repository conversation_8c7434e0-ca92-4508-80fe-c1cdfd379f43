/**
 * DashboardTabs Component
 *
 * EXTRACTED FROM: TradingDashboard.tsx (lines 122-162 + 320-333)
 * F1 racing-themed tab navigation with accessibility and persistence.
 *
 * BENEFITS:
 * - Reusable tab navigation component
 * - F1 racing aesthetic with red accents
 * - Keyboard navigation support
 * - URL synchronization capability
 * - Local storage persistence
 */
import React from 'react';
export type TabType = 'summary' | 'trades' | 'setups' | 'analytics';
export interface Tab {
    id: TabType;
    label: string;
    icon?: string;
    disabled?: boolean;
}
export interface DashboardTabsProps {
    /** Currently active tab */
    activeTab: TabType;
    /** Callback when tab changes */
    onTabChange: (tab: TabType) => void;
    /** Available tabs */
    tabs?: Tab[];
    /** Custom className */
    className?: string;
    /** Whether to persist tab state in localStorage */
    persistState?: boolean;
    /** Storage key for persistence */
    storageKey?: string;
    /** Whether to sync with URL */
    syncWithUrl?: boolean;
}
/**
 * DashboardTabs Component
 *
 * A reusable F1 racing-themed tab navigation component with accessibility,
 * persistence, and URL synchronization capabilities.
 *
 * @example
 * ```typescript
 * <DashboardTabs
 *   activeTab={activeTab}
 *   onTabChange={setActiveTab}
 *   persistState={true}
 *   syncWithUrl={true}
 * />
 * ```
 */
export declare const DashboardTabs: React.FC<DashboardTabsProps>;
export default DashboardTabs;
//# sourceMappingURL=DashboardTabs.d.ts.map