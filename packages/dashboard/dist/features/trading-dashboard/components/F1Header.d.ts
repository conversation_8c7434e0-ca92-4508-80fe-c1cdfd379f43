/**
 * F1Header Component
 *
 * EXTRACTED FROM: TradingDashboard.tsx (lines 28-94 + 302-308)
 * F1 racing-themed header with live session indicator and refresh functionality.
 *
 * BENEFITS:
 * - Reusable F1-themed header across features
 * - Consistent live session indicator
 * - Centralized refresh functionality
 * - Responsive design with F1 aesthetic
 */
import React from 'react';
export interface F1HeaderProps {
    /** Whether the trading session is live */
    isLive?: boolean;
    /** Name of the current trading session */
    sessionName?: string;
    /** Refresh callback function */
    onRefresh?: () => void;
    /** Whether refresh is in progress */
    isRefreshing?: boolean;
    /** Custom className for styling */
    className?: string;
    /** Additional header actions */
    actions?: React.ReactNode;
}
/**
 * F1Header Component
 *
 * A reusable F1 racing-themed header component with live session indicator
 * and refresh functionality. Extracted from TradingDashboard for reusability.
 *
 * @example
 * ```typescript
 * <F1Header
 *   isLive={true}
 *   sessionName="London Session"
 *   onRefresh={handleRefresh}
 *   isRefreshing={isLoading}
 * />
 * ```
 */
export declare const F1Header: React.FC<F1HeaderProps>;
export default F1Header;
//# sourceMappingURL=F1Header.d.ts.map