/**
 * Metrics Panel Component
 *
 * Displays key trading metrics in a card format
 */
import React from 'react';
import { PerformanceMetric } from '../types';
interface MetricsPanelProps {
    metrics: PerformanceMetric[];
    isLoading?: boolean;
}
/**
 * F1 Racing MetricsPanel Component
 *
 * Displays key trading metrics in F1-inspired performance cards
 */
export declare const MetricsPanel: React.FC<MetricsPanelProps>;
export default MetricsPanel;
//# sourceMappingURL=MetricsPanel.d.ts.map