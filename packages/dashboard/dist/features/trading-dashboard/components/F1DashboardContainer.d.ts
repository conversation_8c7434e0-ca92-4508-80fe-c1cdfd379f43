/**
 * F1DashboardContainer Component
 *
 * REFACTORED FROM: TradingDashboard.tsx (388 lines → focused components)
 * Main orchestrator for trading dashboard with F1 container pattern.
 *
 * BENEFITS:
 * - Uses F1Container for consistent styling
 * - Separates orchestration from presentation
 * - Better error handling and loading states
 * - Follows proven container pattern
 * - F1 racing theme integration
 */
import React from 'react';
export interface F1DashboardContainerProps {
    /** Custom className */
    className?: string;
    /** Initial tab to display */
    initialTab?: 'summary' | 'trades' | 'setups' | 'analytics';
}
/**
 * F1DashboardContainer Component
 *
 * PATTERN: F1 Container Pattern
 * - Error boundaries and loading states
 * - Consistent F1 styling and theme
 * - Proper separation of concerns
 * - Suspense for code splitting
 */
export declare const F1DashboardContainer: React.FC<F1DashboardContainerProps>;
export default F1DashboardContainer;
//# sourceMappingURL=F1DashboardContainer.d.ts.map