import { jsxs as _jsxs, jsx as _jsx } from 'react/jsx-runtime';
import styled from 'styled-components';
const ActionsContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.md || '12px'};
  padding-top: ${({ theme }) => theme.spacing?.md || '12px'};
  border-top: 1px solid var(--border-primary);
`;
const ButtonRow = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing?.md || '12px'};
  justify-content: flex-end;

  @media (max-width: 480px) {
    flex-direction: column;
  }
`;
const SubmitButton = styled.button`
  padding: ${({ theme }) => theme.spacing?.md || '12px'}
    ${({ theme }) => theme.spacing?.xl || '24px'};
  background: ${({ $canSubmit, theme }) =>
    $canSubmit ? theme.colors?.primary || 'var(--primary-color)' : 'var(--border-primary)'};
  color: white;
  border: none;
  border-radius: ${({ theme }) => theme.borderRadius?.md || '6px'};
  font-weight: 600;
  font-size: ${({ theme }) => theme.fontSizes?.md || '1rem'};
  cursor: ${({ $canSubmit }) => ($canSubmit ? 'pointer' : 'not-allowed')};
  transition: all 0.2s ease;
  min-width: 140px;
  position: relative;
  text-transform: uppercase;
  letter-spacing: 0.025em;

  &:hover {
    background: ${({ $canSubmit, theme }) =>
      $canSubmit ? theme.colors?.primaryDark || 'var(--primary-dark)' : 'var(--border-primary)'};
    transform: ${({ $canSubmit }) => ($canSubmit ? 'translateY(-1px)' : 'none')};
  }

  &:active {
    transform: ${({ $canSubmit }) => ($canSubmit ? 'translateY(0)' : 'none')};
  }

  /* Loading spinner */
  ${({ $isSubmitting }) =>
    $isSubmitting &&
    `
    &::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 16px;
      height: 16px;
      margin: -8px 0 0 -8px;
      border: 2px solid transparent;
      border-top: 2px solid currentColor;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  `}
`;
const ClearButton = styled.button`
  padding: ${({ theme }) => theme.spacing?.md || '12px'}
    ${({ theme }) => theme.spacing?.lg || '16px'};
  background: transparent;
  color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};
  border: 1px solid var(--border-primary);
  border-radius: ${({ theme }) => theme.borderRadius?.md || '6px'};
  font-weight: 500;
  font-size: ${({ theme }) => theme.fontSizes?.md || '1rem'};
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 100px;

  &:hover {
    background: var(--border-primary);
    color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
    border-color: var(--text-secondary);
  }
`;
const StatusRow = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};

  @media (max-width: 480px) {
    flex-direction: column;
    gap: ${({ theme }) => theme.spacing?.xs || '4px'};
    align-items: flex-start;
  }
`;
const AutoSaveStatus = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing?.xs || '4px'};
  opacity: ${({ $visible }) => ($visible ? 1 : 0.5)};
  transition: opacity 0.3s ease;
`;
const ValidationStatus = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing?.xs || '4px'};
  color: ${({ $valid }) => ($valid ? 'var(--success-color)' : 'var(--error-color)')};
`;
/**
 * Format timestamp for display
 */
const formatLastSaved = date => {
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffSeconds = Math.floor(diffMs / 1000);
  const diffMinutes = Math.floor(diffSeconds / 60);
  if (diffSeconds < 60) {
    return `Saved ${diffSeconds}s ago`;
  } else if (diffMinutes < 60) {
    return `Saved ${diffMinutes}m ago`;
  } else {
    return `Saved at ${date.toLocaleTimeString()}`;
  }
};
/**
 * QuickTradeFormActions Component
 *
 * Renders action buttons and status indicators for the quick trade form.
 */
export const QuickTradeFormActions = ({
  onSubmit,
  onClear,
  isSubmitting,
  canSubmit,
  lastSaved,
}) => {
  return _jsxs(ActionsContainer, {
    children: [
      _jsxs(StatusRow, {
        children: [
          _jsxs(AutoSaveStatus, {
            $visible: !!lastSaved,
            children: [
              '\uD83D\uDCBE ',
              lastSaved ? formatLastSaved(lastSaved) : 'Auto-save enabled',
            ],
          }),
          _jsx(ValidationStatus, {
            $valid: canSubmit,
            children: canSubmit ? '✅ Ready to submit' : '⚠️ Please complete required fields',
          }),
        ],
      }),
      _jsxs(ButtonRow, {
        children: [
          _jsx(ClearButton, {
            type: 'button',
            onClick: onClear,
            disabled: isSubmitting,
            children: 'Clear Form',
          }),
          _jsx(SubmitButton, {
            type: 'submit',
            onClick: onSubmit,
            $isSubmitting: isSubmitting,
            $canSubmit: canSubmit && !isSubmitting,
            disabled: !canSubmit || isSubmitting,
            children: isSubmitting ? 'Submitting...' : '🚀 Submit Trade',
          }),
        ],
      }),
    ],
  });
};
export default QuickTradeFormActions;
//# sourceMappingURL=QuickTradeFormActions.js.map
