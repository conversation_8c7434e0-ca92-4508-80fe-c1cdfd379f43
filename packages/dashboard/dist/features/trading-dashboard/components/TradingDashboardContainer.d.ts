/**
 * TradingDashboardContainer Component
 *
 * FINAL CONTAINER: Main orchestrator for the refactored trading dashboard
 * Provides error boundaries, layout management, and component composition.
 *
 * BENEFITS:
 * - Clean separation of concerns
 * - Comprehensive error boundaries
 * - Responsive layout management
 * - Feature flag support for gradual migration
 * - Performance optimized composition
 */
import React from 'react';
export interface TradingDashboardContainerProps {
    /** Custom className */
    className?: string;
    /** Whether to enable feature flags (unused but kept for compatibility) */
    _enableFeatureFlags?: boolean;
    /** Initial tab to display */
    initialTab?: 'summary' | 'trades' | 'setups' | 'analytics';
}
/**
 * TradingDashboardContainer Component
 *
 * Main container that provides context, error boundaries, and layout management
 * for the refactored trading dashboard. This is the final orchestrator component.
 */
export declare const TradingDashboardContainer: React.FC<TradingDashboardContainerProps>;
export default TradingDashboardContainer;
//# sourceMappingURL=TradingDashboardContainer.d.ts.map