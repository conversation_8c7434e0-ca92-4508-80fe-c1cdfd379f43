/**
 * useDashboardNavigation Hook
 *
 * REFACTORED FROM: TradingDashboard.tsx (388 lines → focused components)
 * Hook for managing dashboard navigation and tab state.
 *
 * BENEFITS:
 * - Focused responsibility (navigation only)
 * - Persistent tab state with localStorage
 * - Type-safe navigation handling
 * - Reusable across dashboard components
 * - Better separation of concerns
 */
import { DashboardTab } from './F1DashboardTabs';
export interface UseDashboardNavigationProps {
    /** Default tab to show */
    defaultTab?: DashboardTab;
    /** Storage key for persistence */
    storageKey?: string;
}
export interface UseDashboardNavigationReturn {
    /** Current active tab */
    activeTab: DashboardTab;
    /** Change active tab */
    setActiveTab: (tab: DashboardTab) => void;
    /** Navigate to next tab */
    nextTab: () => void;
    /** Navigate to previous tab */
    previousTab: () => void;
    /** Check if tab is active */
    isTabActive: (tab: DashboardTab) => boolean;
    /** Get tab index */
    getTabIndex: (tab: DashboardTab) => number;
    /** Get all available tabs */
    availableTabs: DashboardTab[];
}
/**
 * useDashboardNavigation Hook
 *
 * Manages tab navigation state with persistence and keyboard navigation.
 */
export declare const useDashboardNavigation: ({ defaultTab, storageKey, }?: UseDashboardNavigationProps) => UseDashboardNavigationReturn;
export default useDashboardNavigation;
//# sourceMappingURL=useDashboardNavigation.d.ts.map