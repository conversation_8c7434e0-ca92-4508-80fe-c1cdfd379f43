/**
 * F1DashboardTabs Component
 *
 * REFACTORED FROM: TradingDashboard.tsx (388 lines → focused components)
 * F1 racing-themed tabs for dashboard navigation.
 *
 * BENEFITS:
 * - Focused responsibility (tab navigation only)
 * - F1 racing theme with smooth animations
 * - Consistent with other F1Tab components
 * - Better separation of concerns
 * - Reusable tab navigation pattern
 */
import React from 'react';
export type DashboardTab = 'summary' | 'trades' | 'setups' | 'analytics';
export interface F1DashboardTabsProps {
    /** Currently active tab */
    activeTab: DashboardTab;
    /** Tab change handler */
    onTabChange: (tab: DashboardTab) => void;
    /** Whether tabs are disabled */
    disabled?: boolean;
    /** Custom className */
    className?: string;
}
/**
 * F1DashboardTabs Component
 *
 * PATTERN: F1 Tabs Pattern
 * - Racing-inspired styling with red accents
 * - Smooth hover animations and transitions
 * - Clear visual feedback for active state
 * - Accessible keyboard navigation
 * - Responsive design for mobile
 */
export declare const F1DashboardTabs: React.FC<F1DashboardTabsProps>;
export default F1DashboardTabs;
//# sourceMappingURL=F1DashboardTabs.d.ts.map