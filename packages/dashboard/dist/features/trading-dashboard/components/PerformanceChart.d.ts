/**
 * Performance Chart Component
 *
 * Displays a chart showing trading performance over time
 */
import React from 'react';
import { ChartDataPoint } from '../types';
interface PerformanceChartProps {
    data: ChartDataPoint[];
    isLoading?: boolean;
}
/**
 * PerformanceChart Component
 *
 * Displays a chart showing trading performance over time
 */
export declare const PerformanceChart: React.FC<PerformanceChartProps>;
export default PerformanceChart;
//# sourceMappingURL=PerformanceChart.d.ts.map