{"version": 3, "file": "MetricsPanel.js", "sourceRoot": "", "sources": ["../../../../src/features/trading-dashboard/components/MetricsPanel.tsx"], "names": [], "mappings": ";AAOA,OAAO,MAAM,MAAM,mBAAmB,CAAC;AAQvC,6BAA6B;AAC7B,MAAM,SAAS,GAAG,MAAM,CAAC,GAAG,CAAA;;;SAGnB,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;mBACrB,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;CACjD,CAAC;AAEF,wBAAwB;AACxB,MAAM,UAAU,GAAG,MAAM,CAAC,GAAG,CAAA;;;MAGvB,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO;;;;mBAItB,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE;aAC1C,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;;;;;;;;oBAQxB,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA4B1D,CAAC;AAEF,yBAAyB;AACzB,MAAM,WAAW,GAAG,MAAM,CAAC,EAAE,CAAA;eACd,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE;WACrC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,aAAa;gBACpC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;;;;;;CAM9C,CAAC;AAEF,yBAAyB;AACzB,MAAM,WAAW,GAAG,MAAM,CAAC,GAAG,CAAA;eACf,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE;;WAErC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,WAAW;;;;;CAKjD,CAAC;AAEF,oCAAoC;AACpC,MAAM,YAAY,GAAG,MAAM,CAAC,GAAG,CAA0B;eAC1C,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE;WACrC,CAAC,EAAE,KAAK,EAAE,UAAU,EAAE,EAAE,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC;;;gBAG9E,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;;;;;;;;eAQhC,CAAC,EAAE,UAAU,EAAE,EAAE,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC;oBAC3C,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;;;CAGlD,CAAC;AAEF,8BAA8B;AAC9B,MAAM,gBAAgB,GAAG,MAAM,CAAC,GAAG,CAAA;;;eAGpB,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;CAyB/C,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,MAAM,YAAY,GAAgC,CAAC,EAAE,OAAO,EAAE,SAAS,GAAG,KAAK,EAAE,EAAE,EAAE;IAC1F,IAAI,SAAS,EAAE,CAAC;QACd,OAAO,CACL,KAAC,SAAS,cACP,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CACvB,MAAC,UAAU,eACT,KAAC,WAAW,+BAA2B,EACvC,KAAC,gBAAgB,qCAAsC,KAFxC,CAAC,CAGL,CACd,CAAC,GACQ,CACb,CAAC;IACJ,CAAC;IAED,OAAO,CACL,KAAC,SAAS,cACP,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC,CAC9B,MAAC,UAAU,eACT,KAAC,WAAW,cAAE,MAAM,CAAC,KAAK,GAAe,EACzC,KAAC,WAAW,cAAE,MAAM,CAAC,KAAK,GAAe,EACxC,MAAM,CAAC,MAAM,KAAK,SAAS,IAAI,CAC9B,MAAC,YAAY,IAAC,UAAU,EAAE,MAAM,CAAC,UAAU,aACxC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,QAAI,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,IAClD,CAChB,KAPc,KAAK,CAQT,CACd,CAAC,GACQ,CACb,CAAC;AACJ,CAAC,CAAC;AAEF,eAAe,YAAY,CAAC"}