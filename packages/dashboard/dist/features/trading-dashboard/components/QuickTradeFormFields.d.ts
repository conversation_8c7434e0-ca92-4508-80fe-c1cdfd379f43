/**
 * QuickTradeFormFields Component
 *
 * Form fields for the quick trade entry form using the new F1 component library.
 * Extracted from the original QuickTradeForm for better separation of concerns.
 */
import React from 'react';
import { UseFormFieldReturn } from '@adhd-trading-dashboard/shared';
export interface QuickTradeFormFieldsProps {
    /** Date field */
    dateField: UseFormFieldReturn;
    /** Symbol field */
    symbolField: UseFormFieldReturn;
    /** Direction field */
    directionField: UseFormFieldReturn;
    /** Quantity field */
    quantityField: UseFormFieldReturn;
    /** Entry price field */
    entryPriceField: UseFormFieldReturn;
    /** Exit price field */
    exitPriceField: UseFormFieldReturn;
}
/**
 * QuickTradeFormFields Component
 *
 * Renders all form fields for the quick trade entry form
 * using the standardized F1FormField components.
 */
export declare const QuickTradeFormFields: React.FC<QuickTradeFormFieldsProps>;
export default QuickTradeFormFields;
//# sourceMappingURL=QuickTradeFormFields.d.ts.map