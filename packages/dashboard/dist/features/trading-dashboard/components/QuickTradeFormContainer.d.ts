/**
 * QuickTradeFormContainer Component
 *
 * REFACTORED: Main orchestrator for the quick trade form feature
 * Follows the proven container pattern from TradeAnalysis and TradingPlan.
 *
 * BENEFITS:
 * - Uses new F1 component library
 * - Clean separation of concerns
 * - Comprehensive error handling
 * - Auto-save functionality
 * - Performance optimized
 */
import React from 'react';
import { TradeFormData } from '@adhd-trading-dashboard/shared';
export interface QuickTradeFormContainerProps {
    /** Callback when trade is successfully submitted */
    onSubmit?: (trade: TradeFormData) => Promise<void>;
    /** Initial form values */
    initialValues?: Partial<TradeFormData>;
    /** Custom className */
    className?: string;
    /** Whether to enable auto-save */
    autoSave?: boolean;
    /** Auto-save interval in milliseconds */
    autoSaveInterval?: number;
}
/**
 * QuickTradeFormContainer Component
 *
 * Main container that orchestrates the quick trade form functionality
 * using the new F1 component library patterns.
 */
export declare const QuickTradeFormContainer: React.FC<QuickTradeFormContainerProps>;
export default QuickTradeFormContainer;
//# sourceMappingURL=QuickTradeFormContainer.d.ts.map