/**
 * Setup Analysis Component
 *
 * Displays performance metrics for different trading setups
 */
import React from 'react';
import { SetupPerformance, SessionPerformance } from '../types';
interface SetupAnalysisProps {
    setupPerformance: SetupPerformance[];
    sessionPerformance: SessionPerformance[];
    isLoading?: boolean;
}
/**
 * SetupAnalysis Component
 *
 * Displays performance metrics for different trading setups
 */
export declare const SetupAnalysis: React.FC<SetupAnalysisProps>;
export default SetupAnalysis;
//# sourceMappingURL=SetupAnalysis.d.ts.map