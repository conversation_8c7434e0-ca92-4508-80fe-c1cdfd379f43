/**
 * QuickTradeFormActions Component
 *
 * Action buttons and status indicators for the quick trade form.
 * Extracted from the original QuickTradeForm for better separation of concerns.
 */
import React from 'react';
export interface QuickTradeFormActionsProps {
    /** Submit handler */
    onSubmit: () => void;
    /** Clear handler */
    onClear: () => void;
    /** Whether form is submitting */
    isSubmitting: boolean;
    /** Whether form can be submitted */
    canSubmit: boolean;
    /** Last saved timestamp */
    lastSaved?: Date | null;
}
/**
 * QuickTradeFormActions Component
 *
 * Renders action buttons and status indicators for the quick trade form.
 */
export declare const QuickTradeFormActions: React.FC<QuickTradeFormActionsProps>;
export default QuickTradeFormActions;
//# sourceMappingURL=QuickTradeFormActions.d.ts.map