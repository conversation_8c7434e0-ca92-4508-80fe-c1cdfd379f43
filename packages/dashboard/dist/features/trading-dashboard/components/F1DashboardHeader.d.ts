/**
 * F1DashboardHeader Component
 *
 * REFACTORED FROM: TradingDashboard.tsx (388 lines → focused components)
 * F1 racing-themed header for the trading dashboard feature.
 *
 * BENEFITS:
 * - Focused responsibility (header only)
 * - F1 racing theme with live session indicators
 * - Consistent with other F1Header components
 * - Better separation of concerns
 * - Reusable across dashboard contexts
 */
import React from 'react';
export interface F1DashboardHeaderProps {
    /** Custom className */
    className?: string;
    /** Whether data is loading */
    isLoading?: boolean;
    /** Whether refresh is in progress */
    isRefreshing?: boolean;
    /** Current session number */
    sessionNumber?: number;
    /** Whether session is live */
    isLiveSession?: boolean;
    /** Refresh handler */
    onRefresh?: () => void;
    /** Custom title */
    title?: string;
}
/**
 * F1DashboardHeader Component
 *
 * PATTERN: F1 Header Pattern
 * - Racing-inspired styling with live session indicators
 * - Animated status indicators and refresh button
 * - Consistent with F1 design system
 * - Accessible and responsive
 * - Professional trading dashboard appearance
 */
export declare const F1DashboardHeader: React.FC<F1DashboardHeaderProps>;
export default F1DashboardHeader;
//# sourceMappingURL=F1DashboardHeader.d.ts.map