/**
 * useSettingsForm Hook
 *
 * REFACTORED FROM: useSettings.ts (enhanced with form management)
 * Enhanced hook for managing settings form state, validation, and persistence.
 *
 * BENEFITS:
 * - Focused responsibility (form management only)
 * - Built-in validation and error handling
 * - Optimistic updates with rollback capability
 * - Local storage persistence
 * - Change tracking for unsaved state
 */
export interface SettingsFormData {
    theme: string;
    refreshInterval: number;
    showNotifications: boolean;
    enableAdvancedMetrics: boolean;
    autoSaveJournal: boolean;
}
export interface ValidationErrors {
    [key: string]: string;
}
export interface UseSettingsFormReturn {
    /** Current form data */
    data: SettingsFormData;
    /** Original saved data */
    savedData: SettingsFormData;
    /** Whether form has unsaved changes */
    hasUnsavedChanges: boolean;
    /** Validation errors */
    errors: ValidationErrors;
    /** Whether form is valid */
    isValid: boolean;
    /** Whether save operation is in progress */
    isSaving: boolean;
    /** Change handler */
    handleChange: (name: string, value: any) => void;
    /** Save handler */
    handleSave: () => Promise<void>;
    /** Reset handler */
    handleReset: () => void;
    /** Validate specific field */
    validateField: (name: string, value: any) => string | null;
    /** Validate entire form */
    validateForm: (formData: SettingsFormData) => ValidationErrors;
}
/**
 * useSettingsForm Hook
 *
 * Enhanced settings management with form validation and persistence.
 */
export declare const useSettingsForm: () => UseSettingsFormReturn;
export default useSettingsForm;
//# sourceMappingURL=useSettingsForm.d.ts.map