/**
 * useSettings Hook
 *
 * Custom hook for managing application settings
 */
interface Settings {
    theme: string;
    refreshInterval: number;
    showNotifications: boolean;
    enableAdvancedMetrics: boolean;
    autoSaveJournal: boolean;
}
/**
 * useSettings Hook
 *
 * Provides state management and handlers for user settings
 */
export declare const useSettings: () => {
    settings: Settings;
    handleChange: (name: string, value: any) => void;
    handleSave: () => void;
};
export {};
//# sourceMappingURL=useSettings.d.ts.map