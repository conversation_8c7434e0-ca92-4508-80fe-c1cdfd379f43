/**
 * ToggleSwitch Component
 *
 * Reusable toggle switch component for boolean settings
 */
import React from "react";
interface ToggleSwitchProps {
    checked: boolean;
    onChange: (checked: boolean) => void;
    id?: string;
}
/**
 * ToggleSwitch Component
 *
 * A toggle switch component for boolean settings
 */
declare const ToggleSwitch: React.FC<ToggleSwitchProps>;
export default ToggleSwitch;
//# sourceMappingURL=ToggleSwitch.d.ts.map