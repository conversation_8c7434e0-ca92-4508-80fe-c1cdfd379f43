/**
 * SettingsForm Component
 *
 * REFACTORED FROM: Settings.tsx (271 lines → focused components)
 * F1 racing-themed form with organized sections and validation.
 *
 * BENEFITS:
 * - Focused responsibility (form logic only)
 * - Organized sections with clear hierarchy
 * - F1 racing theme with consistent styling
 * - Built-in validation and error handling
 * - Reusable form field components
 */
import React from 'react';
export interface SettingsFormData {
    theme: string;
    refreshInterval: number;
    showNotifications: boolean;
    enableAdvancedMetrics: boolean;
    autoSaveJournal: boolean;
}
export interface SettingsFormProps {
    /** Form data */
    data: SettingsFormData;
    /** Change handler */
    onChange: (name: string, value: any) => void;
    /** Validation errors */
    errors?: Record<string, string>;
    /** Whether form is disabled */
    disabled?: boolean;
    /** Custom className */
    className?: string;
}
/**
 * SettingsForm Component
 *
 * PATTERN: F1 Form Pattern
 * - Racing-inspired section styling
 * - Organized field groups with clear hierarchy
 * - Consistent form field components
 * - Built-in validation and error handling
 * - Responsive design for all screen sizes
 */
export declare const SettingsForm: React.FC<SettingsFormProps>;
export default SettingsForm;
//# sourceMappingURL=SettingsForm.d.ts.map