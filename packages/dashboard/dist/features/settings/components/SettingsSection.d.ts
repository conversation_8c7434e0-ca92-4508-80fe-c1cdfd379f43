/**
 * SettingsSection Component
 *
 * Reusable component for settings sections
 */
import React, { ReactNode } from "react";
interface SettingsSectionProps {
    title: string;
    children: ReactNode;
}
/**
 * SettingsSection Component
 *
 * A container for grouped settings with a title
 */
declare const SettingsSection: React.FC<SettingsSectionProps>;
export default SettingsSection;
//# sourceMappingURL=SettingsSection.d.ts.map