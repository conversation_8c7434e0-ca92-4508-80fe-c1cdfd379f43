/**
 * SettingsHeader Component
 *
 * REFACTORED FROM: Settings.tsx (271 lines → focused components)
 * F1 racing-themed header for the settings page.
 *
 * BENEFITS:
 * - Focused responsibility (header only)
 * - F1 racing theme with red accents
 * - Reusable across different settings contexts
 * - Consistent with other F1Header components
 * - Better separation of concerns
 */
import React from 'react';
export interface SettingsHeaderProps {
    /** Custom className */
    className?: string;
    /** Whether settings have unsaved changes */
    hasUnsavedChanges?: boolean;
    /** Save handler */
    onSave?: () => void;
    /** Reset handler */
    onReset?: () => void;
}
/**
 * SettingsHeader Component
 *
 * PATTERN: F1 Header Pattern
 * - Racing-inspired styling with red accents
 * - Status indicators with animations
 * - Action buttons with hover effects
 * - Consistent with F1 design system
 * - Accessible and responsive
 */
export declare const SettingsHeader: React.FC<SettingsHeaderProps>;
export default SettingsHeader;
//# sourceMappingURL=SettingsHeader.d.ts.map