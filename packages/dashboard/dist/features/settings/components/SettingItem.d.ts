/**
 * SettingItem Component
 *
 * Reusable component for individual settings
 */
import React, { ReactNode } from "react";
interface SettingItemProps {
    label: string;
    description?: string;
    control: ReactNode;
}
/**
 * SettingItem Component
 *
 * A single setting item with label, description, and control
 */
declare const SettingItem: React.FC<SettingItemProps>;
export default SettingItem;
//# sourceMappingURL=SettingItem.d.ts.map