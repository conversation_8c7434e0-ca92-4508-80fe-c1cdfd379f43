/**
 * SettingsContainer Component
 *
 * REFACTORED FROM: Settings.tsx (271 lines → focused components)
 * Main orchestrator for the settings page with F1 container pattern.
 *
 * BENEFITS:
 * - Uses F1Container for consistent styling
 * - Separates orchestration from presentation
 * - Better error handling and loading states
 * - Follows proven container pattern
 * - F1 racing theme integration
 */
import React from 'react';
export interface SettingsContainerProps {
    /** Custom className */
    className?: string;
}
/**
 * SettingsContainer Component
 *
 * PATTERN: F1 Container Pattern
 * - Error boundaries and loading states
 * - Consistent F1 styling and theme
 * - Proper separation of concerns
 * - Suspense for code splitting
 */
export declare const SettingsContainer: React.FC<SettingsContainerProps>;
export default SettingsContainer;
//# sourceMappingURL=SettingsContainer.d.ts.map