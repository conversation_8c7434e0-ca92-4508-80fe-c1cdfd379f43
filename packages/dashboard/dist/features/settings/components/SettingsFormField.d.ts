/**
 * SettingsFormField Component
 *
 * REFACTORED FROM: Settings.tsx (271 lines → focused components)
 * Reusable form field component with F1 racing theme.
 *
 * BENEFITS:
 * - Focused responsibility (single form field)
 * - Reusable across different settings
 * - F1 racing theme with consistent styling
 * - Built-in validation and error handling
 * - Accessible form controls
 */
import React from 'react';
export type FieldType = 'text' | 'number' | 'select' | 'toggle' | 'textarea';
export interface FieldOption {
    value: string | number;
    label: string;
}
export interface SettingsFormFieldProps {
    /** Field identifier */
    name: string;
    /** Field label */
    label: string;
    /** Field description */
    description?: string;
    /** Field type */
    type: FieldType;
    /** Current value */
    value: any;
    /** Change handler */
    onChange: (name: string, value: any) => void;
    /** Options for select fields */
    options?: FieldOption[];
    /** Input props for text/number fields */
    inputProps?: React.InputHTMLAttributes<HTMLInputElement>;
    /** Validation error */
    error?: string;
    /** Whether field is disabled */
    disabled?: boolean;
    /** Custom className */
    className?: string;
}
/**
 * SettingsFormField Component
 *
 * PATTERN: F1 Form Field Pattern
 * - Racing-inspired styling with red accents
 * - Consistent form controls across field types
 * - Built-in validation and error states
 * - Accessible with proper labels and focus
 * - Responsive design for mobile
 */
export declare const SettingsFormField: React.FC<SettingsFormFieldProps>;
export default SettingsFormField;
//# sourceMappingURL=SettingsFormField.d.ts.map