/**
 * Setup Builder Component
 *
 * Modular setup construction matrix that replaces complex dropdown-based
 * setup classification with atomic elements that can be combined infinitely.
 */
import React from 'react';
import { SetupComponents } from '@adhd-trading-dashboard/shared';
interface SetupBuilderProps {
    onSetupChange: (components: SetupComponents) => void;
    initialComponents?: SetupComponents;
}
declare const SetupBuilder: React.FC<SetupBuilderProps>;
export default SetupBuilder;
//# sourceMappingURL=SetupBuilder.d.ts.map