/**
 * useTradesTableData Hook
 *
 * REFACTORED FROM: TradesTable.tsx (330 lines → focused components)
 * Custom hook for managing trades table data, sorting, and formatting.
 *
 * BENEFITS:
 * - Focused responsibility (data management only)
 * - Reusable across different table implementations
 * - Optimized with useMemo for performance
 * - Clean separation of business logic
 * - Easy to test and maintain
 */
import { Trade } from '../types';
import { SortField, SortDirection } from '../components/TradesTableHeader';
export interface UseTradesTableDataReturn {
    /** Sorted trades array */
    sortedTrades: Trade[];
    /** Current sort field */
    sortField: SortField;
    /** Current sort direction */
    sortDirection: SortDirection;
    /** Sort handler function */
    handleSort: (field: SortField) => void;
    /** Formatting utilities */
    formatters: {
        formatDate: (dateString: string) => string;
        formatCurrency: (value: number) => string;
        formatPercent: (value: number) => string;
    };
    /** Event handlers */
    handlers: {
        getDirectionVariant: (direction: string) => string;
        getStatusVariant: (status: string) => string;
    };
}
/**
 * useTradesTableData Hook
 *
 * Manages sorting, filtering, and formatting for the trades table.
 * Optimized for performance with memoization.
 */
export declare const useTradesTableData: (trades: Trade[]) => UseTradesTableDataReturn;
export default useTradesTableData;
//# sourceMappingURL=useTradesTableData.d.ts.map