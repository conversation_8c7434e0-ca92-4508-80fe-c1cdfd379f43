/**
 * Trade Analysis Page
 *
 * REFACTORED: Now uses the proven TradingDashboard container pattern
 * for better performance, maintainability, and consistency.
 */
import React from 'react';
/**
 * Main Trade Analysis Component
 *
 * Simple wrapper that provides context and renders the container.
 * Follows the proven TradingDashboard architecture pattern.
 */
declare const TradeAnalysis: React.FC;
export default TradeAnalysis;
//# sourceMappingURL=TradeAnalysis.d.ts.map