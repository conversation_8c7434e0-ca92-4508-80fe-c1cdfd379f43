import { jsx as _jsx } from "react/jsx-runtime";
import { TradeAnalysisProvider } from './hooks/TradeAnalysisContext';
import { TradeAnalysisContainer } from './components/TradeAnalysisContainer';
/**
 * Main Trade Analysis Component
 *
 * Simple wrapper that provides context and renders the container.
 * Follows the proven TradingDashboard architecture pattern.
 */
const TradeAnalysis = () => {
    return (_jsx(TradeAnalysisProvider, { children: _jsx(TradeAnalysisContainer, {}) }));
};
export default TradeAnalysis;
//# sourceMappingURL=TradeAnalysis.js.map