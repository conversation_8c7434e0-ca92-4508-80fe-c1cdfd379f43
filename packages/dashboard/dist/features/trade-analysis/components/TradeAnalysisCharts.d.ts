/**
 * Trade Analysis Charts Component
 *
 * A component for displaying trade analysis charts.
 */
import React from 'react';
import { EquityPoint, DistributionBar } from '../types';
export interface TradeAnalysisChartsProps {
    /** The equity curve data */
    equityCurveData: EquityPoint[];
    /** The distribution data */
    distributionData: DistributionBar[];
    /** Whether the component is in a loading state */
    isLoading?: boolean;
}
/**
 * Trade Analysis Charts Component
 *
 * A component for displaying trade analysis charts.
 */
export declare const TradeAnalysisCharts: React.FC<TradeAnalysisChartsProps>;
//# sourceMappingURL=TradeAnalysisCharts.d.ts.map