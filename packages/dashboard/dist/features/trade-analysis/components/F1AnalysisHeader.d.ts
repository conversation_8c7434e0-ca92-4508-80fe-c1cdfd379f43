/**
 * F1AnalysisHeader Component
 *
 * REFACTORED FROM: TradeAnalysis.tsx (144 lines → focused components)
 * F1 racing-themed header for the trade analysis feature.
 *
 * BENEFITS:
 * - Focused responsibility (header only)
 * - F1 racing theme with red accents
 * - Consistent with other F1Header components
 * - Better separation of concerns
 * - Reusable across analysis contexts
 */
import React from 'react';
export interface F1AnalysisHeaderProps {
    /** Custom className */
    className?: string;
    /** Whether data is loading */
    isLoading?: boolean;
    /** Whether refresh is in progress */
    isRefreshing?: boolean;
    /** Number of trades */
    tradeCount?: number;
    /** Refresh handler */
    onRefresh?: () => void;
    /** Export handler */
    onExport?: () => void;
}
/**
 * F1AnalysisHeader Component
 *
 * PATTERN: F1 Header Pattern
 * - Racing-inspired styling with red accents
 * - Status indicators with animations
 * - Action buttons with hover effects
 * - Consistent with F1 design system
 * - Accessible and responsive
 */
export declare const F1AnalysisHeader: React.FC<F1AnalysisHeaderProps>;
export default F1AnalysisHeader;
//# sourceMappingURL=F1AnalysisHeader.d.ts.map