/**
 * TabContentRenderer Component
 *
 * Renders the appropriate content for each analysis tab.
 * Extracted from the original TradeAnalysis component for better separation of concerns.
 */
import React from 'react';
export interface TabContentRendererProps {
    /** Currently active tab */
    activeTab: string;
    /** Analysis data */
    data: any;
    /** Loading state */
    isLoading: boolean;
    /** Error message */
    error: string | null;
}
/**
 * TabContentRenderer Component
 *
 * Handles rendering of content for each analysis tab with proper
 * error handling and loading states.
 */
export declare const TabContentRenderer: React.FC<TabContentRendererProps>;
export default TabContentRenderer;
//# sourceMappingURL=TabContentRenderer.d.ts.map