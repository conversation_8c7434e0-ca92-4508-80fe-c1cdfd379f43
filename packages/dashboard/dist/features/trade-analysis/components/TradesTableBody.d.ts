/**
 * TradesTableBody Component
 *
 * REFACTORED FROM: TradesTable.tsx (330 lines → focused components)
 * Table body with optimized row rendering and F1 styling.
 *
 * BENEFITS:
 * - Focused responsibility (body rendering only)
 * - Optimized for performance with React.memo
 * - F1 racing theme with hover effects
 * - Clean separation from header logic
 * - Reusable row components
 */
import React from 'react';
import { Trade } from '../types';
export interface TradesTableBodyProps {
    /** Array of trades to display */
    trades: Trade[];
    /** Currently selected trade ID */
    selectedTradeId: string | null;
    /** Row click handler */
    onRowClick: (tradeId: string) => void;
    /** Formatting utilities */
    formatters: {
        formatDate: (dateString: string) => string;
        formatCurrency: (value: number) => string;
        formatPercent: (value: number) => string;
    };
    /** Event handlers */
    handlers: {
        getDirectionVariant: (direction: string) => string;
        getStatusVariant: (status: string) => string;
    };
}
/**
 * TradesTableBody Component
 *
 * PATTERN: F1 Component Pattern
 * - Optimized rendering with React.memo
 * - Consistent F1 racing theme
 * - Performance-focused row rendering
 * - Clean prop interface
 * - Accessible table structure
 */
export declare const TradesTableBody: React.FC<TradesTableBodyProps>;
export default TradesTableBody;
//# sourceMappingURL=TradesTableBody.d.ts.map