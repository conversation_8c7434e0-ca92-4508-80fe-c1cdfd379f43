import { jsx as _jsx, jsxs as _jsxs } from 'react/jsx-runtime';
import styled from 'styled-components';
const TableHead = styled.thead`
  background: ${({ theme }) => theme.colors?.background || 'var(--bg-primary)'};
  position: sticky;
  top: 0;
  z-index: 10;
  border-bottom: 2px solid ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};
`;
const TableRow = styled.tr`
  background: ${({ theme }) => theme.colors?.background || 'var(--bg-primary)'};
`;
const TableHeaderCell = styled.th`
  padding: ${({ theme }) => theme.spacing?.md || '12px'}
    ${({ theme }) => theme.spacing?.sm || '8px'};
  text-align: left;
  font-weight: 600;
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  color: ${({ theme, $active }) =>
    $active
      ? theme.colors?.primary || 'var(--primary-color)'
      : theme.colors?.textPrimary || '#ffffff'};
  cursor: ${({ $sortable }) => ($sortable ? 'pointer' : 'default')};
  user-select: none;
  transition: all 0.2s ease;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  border-bottom: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};

  /* F1 Racing hover effect */
  &:hover {
    ${({ $sortable, theme }) =>
      $sortable &&
      `
      color: ${theme.colors?.primary || 'var(--primary-color)'};
      background: ${theme.colors?.primary || 'var(--primary-color)'}08;
      transform: translateY(-1px);
    `}
  }

  &:active {
    ${({ $sortable }) =>
      $sortable &&
      `
      transform: translateY(0);
    `}
  }
`;
const SortIcon = styled.span`
  display: inline-block;
  margin-left: ${({ theme }) => theme.spacing?.xs || '4px'};
  font-size: 12px;
  opacity: ${({ $active }) => ($active ? 1 : 0.5)};
  transition: all 0.2s ease;
  color: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};

  &::after {
    content: '${({ $direction }) => ($direction === 'asc' ? '↑' : '↓')}';
  }
`;
const HeaderContent = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 20px;
`;
const HeaderText = styled.span`
  flex: 1;
`;
/**
 * Column Configuration
 *
 * Defines the table columns with their properties.
 */
const COLUMNS = [
  {
    key: 'entryTime',
    label: 'Date/Time',
    sortable: true,
    width: '140px',
  },
  {
    key: 'symbol',
    label: 'Symbol',
    sortable: true,
    width: '80px',
  },
  {
    key: 'direction',
    label: 'Direction',
    sortable: true,
    width: '90px',
  },
  {
    key: null,
    label: 'Entry/Exit',
    sortable: false,
    width: '120px',
  },
  {
    key: 'profitLoss',
    label: 'P&L',
    sortable: true,
    width: '100px',
  },
  {
    key: 'profitLossPercent',
    label: 'P&L %',
    sortable: true,
    width: '80px',
  },
  {
    key: 'status',
    label: 'Status',
    sortable: true,
    width: '90px',
  },
  {
    key: null,
    label: 'Strategy',
    sortable: false,
    width: '120px',
  },
  {
    key: null,
    label: 'Tags',
    sortable: false,
    width: '150px',
  },
];
/**
 * TradesTableHeader Component
 *
 * PATTERN: F1 Component Pattern
 * - Racing-inspired styling with red accents
 * - Smooth hover animations and transitions
 * - Clear visual feedback for sorting
 * - Accessible keyboard navigation
 * - Consistent with F1 design system
 */
export const TradesTableHeader = ({ sortField, sortDirection, onSort }) => {
  const handleHeaderClick = columnKey => {
    if (columnKey && onSort) {
      onSort(columnKey);
    }
  };
  const handleKeyDown = (event, columnKey) => {
    if ((event.key === 'Enter' || event.key === ' ') && columnKey) {
      event.preventDefault();
      onSort(columnKey);
    }
  };
  return _jsx(TableHead, {
    children: _jsx(TableRow, {
      children: COLUMNS.map((column, index) =>
        _jsx(
          TableHeaderCell,
          {
            $sortable: column.sortable,
            $active: column.key === sortField,
            style: { width: column.width },
            onClick: () => handleHeaderClick(column.key),
            onKeyDown: e => handleKeyDown(e, column.key),
            tabIndex: column.sortable ? 0 : -1,
            role: column.sortable ? 'button' : undefined,
            'aria-sort':
              column.key === sortField
                ? sortDirection === 'asc'
                  ? 'ascending'
                  : 'descending'
                : undefined,
            title: column.sortable
              ? `Sort by ${column.label} ${
                  column.key === sortField
                    ? sortDirection === 'asc'
                      ? '(descending)'
                      : '(ascending)'
                    : ''
                }`
              : undefined,
            children: _jsxs(HeaderContent, {
              children: [
                _jsx(HeaderText, { children: column.label }),
                column.sortable &&
                  _jsx(SortIcon, { $direction: sortDirection, $active: column.key === sortField }),
              ],
            }),
          },
          index
        )
      ),
    }),
  });
};
export default TradesTableHeader;
//# sourceMappingURL=TradesTableHeader.js.map
