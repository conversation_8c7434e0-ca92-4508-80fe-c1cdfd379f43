/**
 * AnalysisTabs Component
 *
 * F1-themed tab navigation for Trade Analysis following the proven DashboardTabs pattern.
 * Provides consistent navigation experience across the application.
 */
import React from 'react';
export type AnalysisTabType = 'summary' | 'trades' | 'symbols' | 'strategies' | 'timeframes' | 'time';
export interface AnalysisTabsProps {
    /** Currently active tab */
    activeTab: string;
    /** Function called when tab changes */
    onTabChange: (tab: AnalysisTabType) => void;
    /** Custom className */
    className?: string;
}
/**
 * AnalysisTabs Component
 *
 * F1-themed tab navigation that provides consistent navigation
 * experience across the Trade Analysis feature.
 */
export declare const AnalysisTabs: React.FC<AnalysisTabsProps>;
export default AnalysisTabs;
//# sourceMappingURL=AnalysisTabs.d.ts.map