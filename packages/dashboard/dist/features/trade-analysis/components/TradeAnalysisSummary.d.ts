/**
 * Trade Analysis Summary Component
 *
 * A component for displaying trade analysis summary metrics.
 */
import React from 'react';
import { TradeSummary } from '../types';
export interface TradeAnalysisSummaryProps {
    /** The trade summary data */
    summary: TradeSummary;
    /** Whether the component is in a loading state */
    isLoading?: boolean;
}
/**
 * Trade Analysis Summary Component
 *
 * A component for displaying trade analysis summary metrics.
 */
export declare const TradeAnalysisSummary: React.FC<TradeAnalysisSummaryProps>;
//# sourceMappingURL=TradeAnalysisSummary.d.ts.map