/**
 * AnalysisHeader Component
 *
 * F1-themed header for Trade Analysis following the proven F1Header pattern.
 * Provides consistent branding and navigation across the application.
 */
import React from 'react';
export interface AnalysisHeaderProps {
    /** Function called when refresh is clicked */
    onRefresh?: () => void;
    /** Whether refresh is in progress */
    isRefreshing?: boolean;
    /** Custom className */
    className?: string;
}
/**
 * AnalysisHeader Component
 *
 * F1-themed header that provides consistent branding and actions
 * for the Trade Analysis feature.
 */
export declare const AnalysisHeader: React.FC<AnalysisHeaderProps>;
export default AnalysisHeader;
//# sourceMappingURL=AnalysisHeader.d.ts.map