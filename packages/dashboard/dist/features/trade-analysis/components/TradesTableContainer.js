import { jsx as _jsx, jsxs as _jsxs } from 'react/jsx-runtime';
/**
 * TradesTableContainer Component
 *
 * REFACTORED FROM: TradesTable.tsx (330 lines → focused components)
 * Main orchestrator for the trades table with F1 container pattern.
 *
 * BENEFITS:
 * - Uses F1Container for consistent styling
 * - Separates orchestration from presentation
 * - Better error handling and loading states
 * - Follows proven container pattern
 * - F1 racing theme integration
 */
import { Suspense } from 'react';
import styled from 'styled-components';
import { useTradeAnalysis } from '../hooks/TradeAnalysisContext';
import { TradesTableHeader } from './TradesTableHeader';
import { TradesTableBody } from './TradesTableBody';
import { useTradesTableData } from '../hooks/useTradesTableData';
const Container = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
  background: ${({ theme }) => theme.colors?.surface || 'var(--bg-secondary)'};
  border-radius: ${({ theme }) => theme.borderRadius?.lg || '8px'};
  border: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};
  overflow: hidden;
`;
const TableWrapper = styled.div`
  flex: 1;
  overflow: auto;
  position: relative;
`;
const Table = styled.table`
  width: 100%;
  border-collapse: collapse;
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  background: ${({ theme }) => theme.colors?.surface || 'var(--bg-secondary)'};
`;
const EmptyState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: ${({ theme }) => theme.spacing?.xl || '24px'};
  text-align: center;
  color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};
  min-height: 200px;
`;
const EmptyIcon = styled.div`
  font-size: 48px;
  margin-bottom: ${({ theme }) => theme.spacing?.md || '12px'};
  opacity: 0.5;
`;
const EmptyTitle = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes?.lg || '1.125rem'};
  font-weight: 600;
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  margin: 0 0 ${({ theme }) => theme.spacing?.sm || '8px'} 0;
`;
const EmptyDescription = styled.p`
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};
  margin: 0;
  max-width: 300px;
`;
const LoadingFallback = () =>
  _jsxs(EmptyState, {
    children: [
      _jsx(EmptyIcon, { children: '\u23F3' }),
      _jsx(EmptyTitle, { children: 'Loading Trades' }),
      _jsx(EmptyDescription, { children: 'Please wait while we load your trading data...' }),
    ],
  });
// ErrorFallback component removed as unused
/**
 * TradesTableContent Component
 *
 * Renders the table content with proper error handling.
 */
const TradesTableContent = () => {
  const { data, selectedTradeId, selectTrade } = useTradeAnalysis();
  const { sortedTrades, sortField, sortDirection, handleSort, formatters, handlers } =
    useTradesTableData(data?.trades || []);
  // Handle empty state
  if (!data || !data.trades || data.trades.length === 0) {
    return _jsxs(EmptyState, {
      children: [
        _jsx(EmptyIcon, { children: '\uD83D\uDCCA' }),
        _jsx(EmptyTitle, { children: 'No Trades Found' }),
        _jsx(EmptyDescription, {
          children:
            'No trades match your current filters. Try adjusting your search criteria or add some trades to get started.',
        }),
      ],
    });
  }
  // Handle row selection
  const handleRowClick = tradeId => {
    selectTrade(tradeId === selectedTradeId ? null : tradeId);
  };
  return _jsx(TableWrapper, {
    children: _jsxs(Table, {
      children: [
        _jsx(TradesTableHeader, {
          sortField: sortField,
          sortDirection: sortDirection,
          onSort: handleSort,
        }),
        _jsx(TradesTableBody, {
          trades: sortedTrades,
          selectedTradeId: selectedTradeId,
          onRowClick: handleRowClick,
          formatters: formatters,
          handlers: handlers,
        }),
      ],
    }),
  });
};
/**
 * TradesTableContainer Component
 *
 * PATTERN: F1 Container Pattern
 * - Error boundaries and loading states
 * - Consistent F1 styling and theme
 * - Proper separation of concerns
 * - Suspense for code splitting
 */
export const TradesTableContainer = ({ className }) => {
  return _jsx(Container, {
    className: className,
    children: _jsx(Suspense, {
      fallback: _jsx(LoadingFallback, {}),
      children: _jsx(TradesTableContent, {}),
    }),
  });
};
export default TradesTableContainer;
//# sourceMappingURL=TradesTableContainer.js.map
