/**
 * TradesTableContainer Component
 *
 * REFACTORED FROM: TradesTable.tsx (330 lines → focused components)
 * Main orchestrator for the trades table with F1 container pattern.
 *
 * BENEFITS:
 * - Uses F1Container for consistent styling
 * - Separates orchestration from presentation
 * - Better error handling and loading states
 * - Follows proven container pattern
 * - F1 racing theme integration
 */
import React from 'react';
export interface TradesTableContainerProps {
    /** Custom className */
    className?: string;
}
/**
 * TradesTableContainer Component
 *
 * PATTERN: F1 Container Pattern
 * - Error boundaries and loading states
 * - Consistent F1 styling and theme
 * - Proper separation of concerns
 * - Suspense for code splitting
 */
export declare const TradesTableContainer: React.FC<TradesTableContainerProps>;
export default TradesTableContainer;
//# sourceMappingURL=TradesTableContainer.d.ts.map