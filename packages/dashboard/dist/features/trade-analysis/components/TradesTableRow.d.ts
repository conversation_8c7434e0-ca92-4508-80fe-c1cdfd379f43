/**
 * TradesTableRow Component
 *
 * REFACTORED FROM: TradesTable.tsx (330 lines → focused components)
 * Individual table row with F1 racing theme and optimized rendering.
 *
 * BENEFITS:
 * - Focused responsibility (single row rendering)
 * - Optimized with React.memo for performance
 * - F1 racing theme with smooth animations
 * - Reusable across different table contexts
 * - Clean prop interface and type safety
 */
import React from 'react';
import { Trade } from '../types';
export interface TradesTableRowProps {
    /** Trade data */
    trade: Trade;
    /** Whether this row is selected */
    isSelected: boolean;
    /** Click handler */
    onClick: () => void;
    /** Formatting utilities */
    formatters: {
        formatDate: (dateString: string) => string;
        formatCurrency: (value: number) => string;
        formatPercent: (value: number) => string;
    };
    /** Event handlers */
    handlers: {
        getDirectionVariant: (direction: string) => string;
        getStatusVariant: (status: string) => string;
    };
}
/**
 * TradesTableRow Component
 *
 * PATTERN: F1 Component Pattern
 * - Racing-inspired hover effects and animations
 * - Optimized rendering with React.memo
 * - Consistent F1 color scheme
 * - Accessible interaction patterns
 * - Clean separation of concerns
 */
export declare const TradesTableRow: React.FC<TradesTableRowProps>;
export default TradesTableRow;
//# sourceMappingURL=TradesTableRow.d.ts.map