/**
 * TradesTableHeader Component
 *
 * REFACTORED FROM: TradesTable.tsx (330 lines → focused components)
 * Sortable table header with F1 racing theme integration.
 *
 * BENEFITS:
 * - Focused responsibility (header logic only)
 * - Reusable sorting functionality
 * - F1 theme colors and interactions
 * - Better accessibility and UX
 * - Clean separation from table body
 */
import React from 'react';
export type SortField = 'entryTime' | 'symbol' | 'direction' | 'profitLoss' | 'profitLossPercent' | 'status';
export type SortDirection = 'asc' | 'desc';
export interface TradesTableHeaderProps {
    /** Current sort field */
    sortField: SortField;
    /** Current sort direction */
    sortDirection: SortDirection;
    /** Sort handler */
    onSort: (field: SortField) => void;
}
/**
 * TradesTableHeader Component
 *
 * PATTERN: F1 Component Pattern
 * - Racing-inspired styling with red accents
 * - Smooth hover animations and transitions
 * - Clear visual feedback for sorting
 * - Accessible keyboard navigation
 * - Consistent with F1 design system
 */
export declare const TradesTableHeader: React.FC<TradesTableHeaderProps>;
export default TradesTableHeader;
//# sourceMappingURL=TradesTableHeader.d.ts.map