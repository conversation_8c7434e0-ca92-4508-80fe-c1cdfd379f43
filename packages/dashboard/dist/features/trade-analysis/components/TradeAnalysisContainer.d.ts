/**
 * TradeAnalysisContainer Component
 *
 * REFACTORED: Main orchestrator for the trade analysis feature
 * Follows the proven TradingDashboard architecture pattern for consistency.
 *
 * BENEFITS:
 * - Clean separation of concerns
 * - Comprehensive error boundaries
 * - F1 racing theme consistency
 * - Performance optimized with memoization
 * - Reusable tab navigation pattern
 */
import React from 'react';
export interface TradeAnalysisContainerProps {
    /** Custom className */
    className?: string;
}
/**
 * TradeAnalysisContainer Component
 *
 * Main container that provides layout management and error boundaries
 * for the refactored trade analysis. Follows TradingDashboard patterns.
 */
export declare const TradeAnalysisContainer: React.FC<TradeAnalysisContainerProps>;
export default TradeAnalysisContainer;
//# sourceMappingURL=TradeAnalysisContainer.d.ts.map