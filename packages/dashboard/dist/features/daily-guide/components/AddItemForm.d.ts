/**
 * AddItemForm Component
 *
 * Focused form component for adding new trading plan items.
 * Extracted from the original TradingPlan component for better separation of concerns.
 */
import React from 'react';
import { TradingPlanItem } from '../types';
export interface AddItemFormProps {
    /** The new item being created */
    newItem: Omit<TradingPlanItem, 'id'>;
    /** Function to update the new item */
    setNewItem: (item: Omit<TradingPlanItem, 'id'>) => void;
    /** Function called when form is submitted */
    onSubmit: (e: React.FormEvent) => void;
    /** Function called when form is cancelled */
    onCancel: () => void;
    /** Custom className */
    className?: string;
}
/**
 * AddItemForm Component
 *
 * Form for adding new trading plan action items with validation
 * and F1-themed styling.
 */
export declare const AddItemForm: React.FC<AddItemFormProps>;
export default AddItemForm;
//# sourceMappingURL=AddItemForm.d.ts.map