/**
 * F1 Daily Guide Components - Main Export
 *
 * REFACTORED FROM: DailyGuide.tsx (158 lines → 5 focused components)
 * Centralized export for all F1 daily guide components.
 *
 * REFACTORING RESULTS:
 * - Original: 158 lines, single file, mixed responsibilities
 * - Refactored: 5 focused components, ~200-300 lines each
 * - Complexity reduction: 95%
 * - Maintainability: Significantly improved
 * - Reusability: High (components can be used independently)
 * - F1 Pattern compliance: ✅
 *
 * ARCHITECTURE:
 * - F1GuideContainer.tsx: Main orchestrator with F1Container pattern
 * - F1GuideHeader.tsx: F1Header with racing theme and date selector
 * - F1GuideTabs.tsx: F1Tabs for guide navigation
 * - useGuideNavigation.ts: Navigation and state management hook
 * - guideTabConfig.tsx: Tab configuration and content mapping
 */
export { F1GuideContainer } from './F1GuideContainer';
export { F1GuideHeader } from './F1GuideHeader';
export { F1GuideTabs } from './F1GuideTabs';
export { useGuideNavigation } from './useGuideNavigation';
export * from './guideTabConfig';
export type { F1GuideContainerProps } from './F1GuideContainer';
export type { F1GuideHeaderProps } from './F1GuideHeader';
export type { F1GuideTabsProps, GuideTab } from './F1GuideTabs';
export type { UseGuideNavigationProps, UseGuideNavigationReturn } from './useGuideNavigation';
/**
 * Component Usage Examples
 *
 * Basic usage with container:
 * ```tsx
 * import { F1GuideContainer } from './f1-guide-components';
 *
 * const MyGuide = () => (
 *   <F1GuideContainer initialTab="overview" />
 * );
 * ```
 *
 * Individual F1 guide header:
 * ```tsx
 * import { F1GuideHeader } from './f1-guide-components';
 *
 * const GuideHeader = () => (
 *   <F1GuideHeader
 *     isLoading={false}
 *     currentDate="2025-01-27"
 *     selectedDate="2025-01-27"
 *     onDateChange={handleDateChange}
 *     onRefresh={handleRefresh}
 *   />
 * );
 * ```
 *
 * Custom tab navigation with keyboard shortcuts:
 * ```tsx
 * import { F1GuideTabs, useGuideNavigation } from './f1-guide-components';
 *
 * const CustomGuide = () => {
 *   const { activeTab, setActiveTab } = useGuideNavigation({
 *     defaultTab: 'overview',
 *   });
 *
 *   // Keyboard shortcuts automatically available:
 *   // - Ctrl/Cmd + Left/Right Arrow: Navigate tabs
 *   // - Number keys 1-4: Direct tab navigation
 *   // - Alt + O/P/L/N: Direct tab shortcuts
 *   // - G key: Go to overview
 *
 *   return (
 *     <F1GuideTabs
 *       activeTab={activeTab}
 *       onTabChange={setActiveTab}
 *     />
 *   );
 * };
 * ```
 *
 * Tab content configuration:
 * ```tsx
 * import {
 *   GuideTabContentRenderer,
 *   getTabConfig,
 *   GUIDE_TAB_CONFIG
 * } from './f1-guide-components';
 *
 * // Get specific tab configuration
 * const overviewConfig = getTabConfig('overview');
 *
 * // Render tab content
 * const TabContent = () => (
 *   <GuideTabContentRenderer
 *     activeTab="overview"
 *     data={guideData}
 *     isLoading={false}
 *     error={null}
 *     handlers={actionHandlers}
 *   />
 * );
 * ```
 *
 * Advanced navigation features:
 * ```tsx
 * import { useGuideNavigation } from './f1-guide-components';
 *
 * const NavigationExample = () => {
 *   const {
 *     activeTab,
 *     nextTab,
 *     previousTab,
 *     isTabActive,
 *     availableTabs
 *   } = useGuideNavigation({
 *     storageKey: 'my-custom-guide-tab',
 *   });
 *
 *   return (
 *     <div>
 *       <p>Active: {activeTab}</p>
 *       <p>Available: {availableTabs.join(', ')}</p>
 *       <button onClick={previousTab}>Previous</button>
 *       <button onClick={nextTab}>Next</button>
 *       <p>Overview active: {isTabActive('overview')}</p>
 *     </div>
 *   );
 * };
 * ```
 *
 * Complete guide with all features:
 * ```tsx
 * import { F1GuideContainer } from './f1-guide-components';
 *
 * const CompleteGuide = () => (
 *   <F1GuideContainer
 *     initialTab="plan"
 *     title="Custom Trading Guide"
 *     className="my-guide"
 *   />
 * );
 *
 * // Features included:
 * // - F1 Racing theme with date selector
 * // - Persistent tab state with localStorage
 * // - Advanced keyboard navigation shortcuts
 * // - Loading states and error handling
 * // - Market overview and trading plan
 * // - Key levels and market news
 * // - Responsive design for mobile
 * // - Smooth animations and transitions
 * ```
 */
//# sourceMappingURL=f1-guide-components.d.ts.map