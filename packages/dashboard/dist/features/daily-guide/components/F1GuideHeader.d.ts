/**
 * F1GuideHeader Component
 *
 * REFACTORED FROM: DailyGuide.tsx (158 lines → focused components)
 * F1 racing-themed header for the daily guide feature.
 *
 * BENEFITS:
 * - Focused responsibility (header only)
 * - F1 racing theme with date selector
 * - Consistent with other F1Header components
 * - Better separation of concerns
 * - Reusable across guide contexts
 */
import React from 'react';
export interface F1GuideHeaderProps {
    /** Custom className */
    className?: string;
    /** Whether data is loading */
    isLoading?: boolean;
    /** Whether refresh is in progress */
    isRefreshing?: boolean;
    /** Current date */
    currentDate?: string;
    /** Selected date */
    selectedDate?: string;
    /** Date change handler */
    onDateChange?: (date: string) => void;
    /** Refresh handler */
    onRefresh?: () => void;
    /** Custom title */
    title?: string;
}
/**
 * F1GuideHeader Component
 *
 * PATTERN: F1 Header Pattern
 * - Racing-inspired styling with date selector
 * - Current date indicator and navigation
 * - Consistent with F1 design system
 * - Accessible and responsive
 * - Professional trading guide appearance
 */
export declare const F1GuideHeader: React.FC<F1GuideHeaderProps>;
export default F1GuideHeader;
//# sourceMappingURL=F1GuideHeader.d.ts.map