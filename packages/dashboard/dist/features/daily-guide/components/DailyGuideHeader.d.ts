/**
 * DailyGuideHeader Component
 *
 * F1-themed header for Daily Guide following the proven F1Header pattern.
 * Provides consistent branding and navigation across the application.
 */
import React from 'react';
export interface DailyGuideHeaderProps {
    /** Current date to display */
    currentDate?: string;
    /** Function called when refresh is clicked */
    onRefresh?: () => void;
    /** Whether refresh is in progress */
    isRefreshing?: boolean;
    /** Custom className */
    className?: string;
}
/**
 * DailyGuideHeader Component
 *
 * F1-themed header that provides consistent branding and actions
 * for the Daily Guide feature.
 */
export declare const DailyGuideHeader: React.FC<DailyGuideHeaderProps>;
export default DailyGuideHeader;
//# sourceMappingURL=DailyGuideHeader.d.ts.map