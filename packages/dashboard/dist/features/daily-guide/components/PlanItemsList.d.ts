/**
 * PlanItemsList Component
 *
 * Focused component for displaying and managing trading plan action items.
 * Extracted from the original TradingPlan component for better separation of concerns.
 */
import React from 'react';
import { TradingPlanItem } from '../types';
export interface PlanItemsListProps {
    /** The list of trading plan items */
    items: TradingPlanItem[];
    /** Function called when an item is toggled */
    onItemToggle?: (id: string, completed: boolean) => void;
    /** Function called when an item is removed */
    onItemRemove?: (id: string) => void;
    /** Custom className */
    className?: string;
}
/**
 * PlanItemsList Component
 *
 * Displays a list of trading plan action items with interactive controls
 * for toggling completion status and removing items.
 */
export declare const PlanItemsList: React.FC<PlanItemsListProps>;
export default PlanItemsList;
//# sourceMappingURL=PlanItemsList.d.ts.map