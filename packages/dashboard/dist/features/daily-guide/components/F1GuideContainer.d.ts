/**
 * F1GuideContainer Component
 *
 * REFACTORED FROM: DailyGuide.tsx (158 lines → focused components)
 * Main orchestrator for daily guide with F1 container pattern.
 *
 * BENEFITS:
 * - Uses F1Container for consistent styling
 * - Separates orchestration from presentation
 * - Better error handling and loading states
 * - Follows proven container pattern
 * - F1 racing theme integration
 */
import React from 'react';
export interface F1GuideContainerProps {
    /** Custom className */
    className?: string;
    /** Initial tab to display */
    initialTab?: 'overview' | 'plan' | 'levels' | 'news';
    /** Custom title */
    title?: string;
}
/**
 * F1GuideContainer Component
 *
 * PATTERN: F1 Container Pattern
 * - Error boundaries and loading states
 * - Consistent F1 styling and theme
 * - Proper separation of concerns
 * - Suspense for code splitting
 */
export declare const F1GuideContainer: React.FC<F1GuideContainerProps>;
export default F1GuideContainer;
//# sourceMappingURL=F1GuideContainer.d.ts.map