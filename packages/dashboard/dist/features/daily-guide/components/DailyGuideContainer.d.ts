/**
 * DailyGuideContainer Component
 *
 * PATTERN VALIDATION: Applying proven container pattern to DailyGuide
 * to validate cross-component-type effectiveness (dashboard → forms → guides).
 *
 * BENEFITS:
 * - Uses F1Container from component library
 * - Consistent error boundaries and loading states
 * - Validates pattern works across different component types
 * - Maintains F1 racing theme consistency
 */
import React from 'react';
export interface DailyGuideContainerProps {
    /** Custom className */
    className?: string;
}
/**
 * DailyGuideContainer Component
 *
 * PATTERN VALIDATION: Demonstrates that our proven container pattern
 * works effectively across different component types:
 * - Dashboard (TradingDashboard) ✅
 * - Forms (QuickTradeForm) ✅
 * - Analysis (TradeAnalysis) ✅
 * - Guides (DailyGuide) ✅
 */
export declare const DailyGuideContainer: React.FC<DailyGuideContainerProps>;
export default DailyGuideContainer;
//# sourceMappingURL=DailyGuideContainer.d.ts.map