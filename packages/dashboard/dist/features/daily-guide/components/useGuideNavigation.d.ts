/**
 * useGuideNavigation Hook
 *
 * REFACTORED FROM: DailyGuide.tsx (158 lines → focused components)
 * Hook for managing guide navigation and tab state.
 *
 * BENEFITS:
 * - Focused responsibility (navigation only)
 * - Persistent tab state with localStorage
 * - Type-safe navigation handling
 * - Reusable across guide components
 * - Better separation of concerns
 */
import { GuideTab } from './F1GuideTabs';
export interface UseGuideNavigationProps {
    /** Default tab to show */
    defaultTab?: GuideTab;
    /** Storage key for persistence */
    storageKey?: string;
}
export interface UseGuideNavigationReturn {
    /** Current active tab */
    activeTab: GuideTab;
    /** Change active tab */
    setActiveTab: (tab: GuideTab) => void;
    /** Navigate to next tab */
    nextTab: () => void;
    /** Navigate to previous tab */
    previousTab: () => void;
    /** Check if tab is active */
    isTabActive: (tab: GuideTab) => boolean;
    /** Get tab index */
    getTabIndex: (tab: GuideTab) => number;
    /** Get all available tabs */
    availableTabs: GuideTab[];
}
/**
 * useGuideNavigation Hook
 *
 * Manages tab navigation state with persistence and keyboard navigation.
 */
export declare const useGuideNavigation: ({ defaultTab, storageKey, }?: UseGuideNavigationProps) => UseGuideNavigationReturn;
export default useGuideNavigation;
//# sourceMappingURL=useGuideNavigation.d.ts.map