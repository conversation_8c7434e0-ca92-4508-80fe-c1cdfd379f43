/**
 * RiskManagementGrid Component
 *
 * Focused component for displaying risk management metrics.
 * Extracted from the original TradingPlan component for better separation of concerns.
 */
import React from 'react';
import { RiskManagement } from '../types';
export interface RiskManagementGridProps {
    /** The risk management data */
    riskManagement: RiskManagement;
    /** Custom className */
    className?: string;
}
/**
 * RiskManagementGrid Component
 *
 * Displays risk management metrics in a responsive grid layout
 * with F1-themed styling and hover effects.
 */
export declare const RiskManagementGrid: React.FC<RiskManagementGridProps>;
export default RiskManagementGrid;
//# sourceMappingURL=RiskManagementGrid.d.ts.map