/**
 * F1GuideTabs Component
 *
 * REFACTORED FROM: DailyGuide.tsx (158 lines → focused components)
 * F1 racing-themed tabs for guide navigation.
 *
 * BENEFITS:
 * - Focused responsibility (tab navigation only)
 * - F1 racing theme with smooth animations
 * - Consistent with other F1Tab components
 * - Better separation of concerns
 * - Reusable tab navigation pattern
 */
import React from 'react';
export type GuideTab = 'overview' | 'plan' | 'levels' | 'news';
export interface F1GuideTabsProps {
    /** Currently active tab */
    activeTab: GuideTab;
    /** Tab change handler */
    onTabChange: (tab: GuideTab) => void;
    /** Whether tabs are disabled */
    disabled?: boolean;
    /** Custom className */
    className?: string;
}
/**
 * F1GuideTabs Component
 *
 * PATTERN: F1 Tabs Pattern
 * - Racing-inspired styling with red accents
 * - Smooth hover animations and transitions
 * - Clear visual feedback for active state
 * - Accessible keyboard navigation
 * - Responsive design for mobile
 */
export declare const F1GuideTabs: React.FC<F1GuideTabsProps>;
export default F1GuideTabs;
//# sourceMappingURL=F1GuideTabs.d.ts.map