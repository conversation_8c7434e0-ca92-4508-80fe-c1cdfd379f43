/**
 * TradingPlanContainer Component
 *
 * REFACTORED: Main orchestrator for the trading plan feature
 * Follows the proven TradeAnalysis/TradingDashboard architecture pattern.
 *
 * BENEFITS:
 * - Clean separation of concerns
 * - Focused components for each responsibility
 * - Reusable form patterns
 * - F1 racing theme consistency
 */
import React from 'react';
import { TradingPlan as TradingPlanType, TradingPlanItem } from '../types';
export interface TradingPlanContainerProps {
    /** The trading plan data */
    tradingPlan: TradingPlanType | null;
    /** Whether the component is in a loading state */
    isLoading?: boolean;
    /** The error message, if any */
    error?: string | null;
    /** Function called when a trading plan item is toggled */
    onItemToggle?: (id: string, completed: boolean) => void;
    /** Function called when a trading plan item is added */
    onItemAdd?: (item: TradingPlanItem) => void;
    /** Function called when a trading plan item is removed */
    onItemRemove?: (id: string) => void;
    /** Additional class name */
    className?: string;
}
/**
 * TradingPlanContainer Component
 *
 * Main container that orchestrates all trading plan functionality
 * with proper error handling and loading states.
 */
export declare const TradingPlanContainer: React.FC<TradingPlanContainerProps>;
export default TradingPlanContainer;
//# sourceMappingURL=TradingPlanContainer.d.ts.map