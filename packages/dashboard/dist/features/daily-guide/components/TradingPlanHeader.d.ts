/**
 * TradingPlanHeader Component
 *
 * F1-themed header for Trading Plan following the proven header pattern.
 * Provides consistent branding and actions across the application.
 */
import React from 'react';
export interface TradingPlanHeaderProps {
    /** Function called when add item is clicked */
    onAddItem?: () => void;
    /** Whether the add form is currently shown */
    showAddForm?: boolean;
    /** Custom className */
    className?: string;
}
/**
 * TradingPlanHeader Component
 *
 * F1-themed header that provides consistent branding and actions
 * for the Trading Plan feature.
 */
export declare const TradingPlanHeader: React.FC<TradingPlanHeaderProps>;
export default TradingPlanHeader;
//# sourceMappingURL=TradingPlanHeader.d.ts.map