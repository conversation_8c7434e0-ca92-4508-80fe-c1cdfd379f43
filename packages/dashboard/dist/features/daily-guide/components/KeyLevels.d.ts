/**
 * Key Levels Component
 *
 * A component for displaying key price levels.
 */
import React from 'react';
import { KeyPriceLevel } from '../types';
export interface KeyLevelsProps {
    /** The key price levels */
    keyLevels: KeyPriceLevel[];
    /** Whether the component is in a loading state */
    isLoading?: boolean;
    /** The error message, if any */
    error?: string | null;
    /** Function called when the refresh button is clicked */
    onRefresh?: () => void;
    /** Additional class name */
    className?: string;
}
/**
 * Key Levels Component
 *
 * A component for displaying key price levels.
 */
export declare const KeyLevels: React.FC<KeyLevelsProps>;
//# sourceMappingURL=KeyLevels.d.ts.map