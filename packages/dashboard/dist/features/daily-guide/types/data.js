/**
 * Data Types
 *
 * REFACTORED FROM: types.ts (214 lines → focused modules)
 * Main data structures and state management types.
 *
 * BENEFITS:
 * - Clear data flow architecture
 * - Centralized state management types
 * - Better TypeScript inference
 * - F1 container pattern compliance
 */
/**
 * Default Daily Guide Data
 *
 * Provides empty/default data structure.
 */
export const DEFAULT_DAILY_GUIDE_DATA = {
    marketOverview: null,
    tradingPlan: null,
    keyPriceLevels: [],
    watchlist: [],
    marketNews: [],
};
/**
 * Default Daily Guide State
 *
 * Provides initial state for the daily guide.
 */
export const DEFAULT_DAILY_GUIDE_STATE = {
    data: DEFAULT_DAILY_GUIDE_DATA,
    isLoading: false,
    error: null,
    selectedDate: new Date().toISOString().split('T')[0], // Today's date
};
/**
 * Validate Daily Guide Data
 *
 * Validates the completeness and consistency of daily guide data.
 */
export function validateDailyGuideData(data) {
    const errors = [];
    const warnings = [];
    // Check market overview
    if (!data.marketOverview) {
        warnings.push('Market overview is not available');
    }
    else {
        if (!data.marketOverview.summary?.trim()) {
            warnings.push('Market summary is empty');
        }
        if (data.marketOverview.indices.length === 0) {
            warnings.push('No market indices data available');
        }
    }
    // Check trading plan
    if (!data.tradingPlan) {
        warnings.push('Trading plan is not set');
    }
    else {
        if (!data.tradingPlan.strategy?.trim()) {
            errors.push('Trading strategy is required');
        }
        if (data.tradingPlan.items.length === 0) {
            warnings.push('No trading plan items defined');
        }
    }
    // Check watchlist
    if (data.watchlist.length === 0) {
        warnings.push('Watchlist is empty');
    }
    // Check for duplicate symbols in watchlist
    const symbols = data.watchlist.map(item => item.symbol);
    const duplicates = symbols.filter((symbol, index) => symbols.indexOf(symbol) !== index);
    if (duplicates.length > 0) {
        errors.push(`Duplicate symbols in watchlist: ${duplicates.join(', ')}`);
    }
    return {
        isValid: errors.length === 0,
        errors,
        warnings,
    };
}
//# sourceMappingURL=data.js.map