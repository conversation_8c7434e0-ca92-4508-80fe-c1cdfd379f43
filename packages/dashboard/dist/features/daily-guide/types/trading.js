/**
 * Trading Types
 *
 * REFACTORED FROM: types.ts (214 lines → focused modules)
 * Trading plan and risk management type definitions.
 *
 * BENEFITS:
 * - Focused responsibility (trading logic only)
 * - Reusable across trading features
 * - Clear separation of concerns
 * - F1 architectural consistency
 */
/**
 * Default Risk Management Settings
 *
 * Provides sensible defaults for risk management.
 */
export const DEFAULT_RISK_MANAGEMENT = {
    maxRiskPerTrade: 1, // 1% per trade
    maxDailyLoss: 500, // $500 daily loss limit
    maxTrades: 5, // Maximum 5 trades per day
    positionSizing: 'fixed-dollar', // Fixed dollar amount per trade
};
/**
 * Trading Plan Validation
 *
 * Validates a trading plan for completeness.
 */
export function validateTradingPlan(plan) {
    const errors = [];
    if (!plan.strategy?.trim()) {
        errors.push('Trading strategy is required');
    }
    if (!plan.items || plan.items.length === 0) {
        errors.push('At least one trading plan item is required');
    }
    if (plan.riskManagement) {
        if (plan.riskManagement.maxRiskPerTrade <= 0 || plan.riskManagement.maxRiskPerTrade > 10) {
            errors.push('Max risk per trade must be between 0.1% and 10%');
        }
        if (plan.riskManagement.maxDailyLoss <= 0) {
            errors.push('Max daily loss must be greater than 0');
        }
        if (plan.riskManagement.maxTrades <= 0 || plan.riskManagement.maxTrades > 20) {
            errors.push('Max trades must be between 1 and 20');
        }
    }
    return errors;
}
/**
 * Trading Plan Item Factory
 *
 * Creates a new trading plan item with defaults.
 */
export function createTradingPlanItem(description, priority = 'medium') {
    return {
        id: `item-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        description,
        priority,
        completed: false,
    };
}
/**
 * Risk Management Calculator
 *
 * Calculates position size based on risk management rules.
 */
export function calculatePositionSize(accountBalance, entryPrice, stopLoss, riskManagement) {
    const riskAmount = accountBalance * (riskManagement.maxRiskPerTrade / 100);
    const riskPerShare = Math.abs(entryPrice - stopLoss);
    if (riskPerShare === 0)
        return 0;
    return Math.floor(riskAmount / riskPerShare);
}
//# sourceMappingURL=trading.js.map