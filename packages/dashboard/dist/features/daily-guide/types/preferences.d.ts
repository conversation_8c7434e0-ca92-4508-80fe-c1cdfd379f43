/**
 * Preferences Types
 *
 * REFACTORED FROM: types.ts (214 lines → focused modules)
 * User preferences and configuration types.
 *
 * BENEFITS:
 * - Isolated user preferences logic
 * - Easy to extend and maintain
 * - Clear configuration management
 * - F1 theme integration ready
 */
import type { RiskManagement } from './trading';
/**
 * Daily Guide User Preferences
 *
 * User-configurable preferences for the daily guide.
 */
export interface DailyGuidePreferences {
    /** Default watchlist symbols */
    defaultWatchlist: string[];
    /** Whether to show economic events */
    showEconomicEvents: boolean;
    /** Whether to show market news */
    showMarketNews: boolean;
    /** The default risk management settings */
    defaultRiskManagement: RiskManagement;
    /** Theme preferences */
    theme: DailyGuideThemePreferences;
    /** Display preferences */
    display: DailyGuideDisplayPreferences;
}
/**
 * Daily Guide Theme Preferences
 *
 * F1 racing theme specific preferences.
 */
export interface DailyGuideThemePreferences {
    /** Whether to use F1 racing theme */
    useF1Theme: boolean;
    /** Primary accent color (F1 red by default) */
    primaryColor: string;
    /** Secondary accent color (F1 silver by default) */
    secondaryColor: string;
    /** Whether to show live indicators */
    showLiveIndicators: boolean;
    /** Animation preferences */
    enableAnimations: boolean;
}
/**
 * Daily Guide Display Preferences
 *
 * Display and layout preferences.
 */
export interface DailyGuideDisplayPreferences {
    /** Default view mode */
    defaultView: 'compact' | 'detailed' | 'cards';
    /** Whether to auto-refresh data */
    autoRefresh: boolean;
    /** Auto-refresh interval in seconds */
    autoRefreshInterval: number;
    /** Whether to show timestamps */
    showTimestamps: boolean;
    /** Date format preference */
    dateFormat: 'US' | 'EU' | 'ISO';
    /** Time format preference */
    timeFormat: '12h' | '24h';
}
/**
 * Default Daily Guide Preferences
 *
 * Provides sensible defaults with F1 theme integration.
 */
export declare const DEFAULT_DAILY_GUIDE_PREFERENCES: DailyGuidePreferences;
/**
 * Preferences Validation
 *
 * Validates user preferences for consistency.
 */
export declare function validatePreferences(preferences: Partial<DailyGuidePreferences>): string[];
/**
 * Preferences Storage Key
 *
 * Local storage key for persisting preferences.
 */
export declare const PREFERENCES_STORAGE_KEY = "adhd-trading-dashboard:daily-guide:preferences";
/**
 * Load Preferences
 *
 * Loads preferences from local storage with fallback to defaults.
 */
export declare function loadPreferences(): DailyGuidePreferences;
/**
 * Save Preferences
 *
 * Saves preferences to local storage.
 */
export declare function savePreferences(preferences: DailyGuidePreferences): void;
/**
 * Reset Preferences
 *
 * Resets preferences to defaults.
 */
export declare function resetPreferences(): DailyGuidePreferences;
//# sourceMappingURL=preferences.d.ts.map