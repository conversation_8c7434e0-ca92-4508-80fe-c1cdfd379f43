/**
 * Market Types
 *
 * REFACTORED FROM: types.ts (214 lines → focused modules)
 * Market-related type definitions for the daily guide feature.
 *
 * BENEFITS:
 * - Focused responsibility (market data only)
 * - Better reusability across features
 * - Easier maintenance and testing
 * - Follows F1 architectural patterns
 */
/**
 * Market Sentiment
 *
 * Represents the overall market direction and mood.
 */
export type MarketSentiment = 'bullish' | 'bearish' | 'neutral';
/**
 * Market Index
 *
 * Represents a major market index with current and historical data.
 */
export interface MarketIndex {
    /** The index symbol (e.g., SPY, QQQ, IWM) */
    symbol: string;
    /** The index name (e.g., S&P 500, NASDAQ 100) */
    name: string;
    /** The current value */
    value: number;
    /** The change from previous close */
    change: number;
    /** The percentage change from previous close */
    changePercent: number;
    /** The previous close value */
    previousClose?: number;
}
/**
 * Economic Event
 *
 * Represents an economic event that may impact trading.
 */
export interface EconomicEvent {
    /** The event title (e.g., "Non-Farm Payrolls") */
    title: string;
    /** The event time (ISO string) */
    time: string;
    /** The event importance level */
    importance: 'high' | 'medium' | 'low';
    /** The expected value */
    expected?: string;
    /** The previous value */
    previous?: string;
    /** The actual value (if released) */
    actual?: string;
}
/**
 * Market News Item
 *
 * Represents a news item that may impact market sentiment.
 */
export interface MarketNewsItem {
    /** The news item ID */
    id: string;
    /** The news item title */
    title: string;
    /** The news item source (e.g., "Reuters", "Bloomberg") */
    source: string;
    /** The news item timestamp (ISO string) */
    timestamp: string;
    /** The news item URL */
    url: string;
    /** The news item impact level */
    impact: 'high' | 'medium' | 'low';
    /** The news item sentiment */
    sentiment?: 'positive' | 'negative' | 'neutral';
}
/**
 * Market Overview
 *
 * Comprehensive market data for the daily guide.
 */
export interface MarketOverview {
    /** The overall market sentiment */
    sentiment: MarketSentiment;
    /** The market summary */
    summary: string;
    /** Major indices data */
    indices: MarketIndex[];
    /** Economic events for the day */
    economicEvents: EconomicEvent[];
    /** Market news items */
    news: MarketNewsItem[];
    /** Last updated timestamp (ISO string) */
    lastUpdated: string;
}
/**
 * Key Price Level
 *
 * Important support and resistance levels for a symbol.
 */
export interface KeyPriceLevel {
    /** The symbol */
    symbol: string;
    /** Support levels (price strings) */
    support: string[];
    /** Resistance levels (price strings) */
    resistance: string[];
    /** Pivot point */
    pivotPoint?: string;
    /** The level description */
    description?: string;
}
/**
 * Watchlist Item
 *
 * A symbol being watched for potential trading opportunities.
 */
export interface WatchlistItem {
    /** The symbol */
    symbol: string;
    /** The company name */
    name: string;
    /** The current price */
    price: number;
    /** The reason for watching */
    reason: string;
    /** The potential trade setup */
    setup?: string;
    /** The entry price */
    entryPrice?: number;
    /** The stop loss price */
    stopLoss?: number;
    /** The take profit price */
    takeProfit?: number;
}
//# sourceMappingURL=market.d.ts.map