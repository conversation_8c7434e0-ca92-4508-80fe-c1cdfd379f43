/**
 * Trading Types
 *
 * REFACTORED FROM: types.ts (214 lines → focused modules)
 * Trading plan and risk management type definitions.
 *
 * BENEFITS:
 * - Focused responsibility (trading logic only)
 * - Reusable across trading features
 * - Clear separation of concerns
 * - F1 architectural consistency
 */
/**
 * Trading Plan Priority
 *
 * Priority levels for trading plan items.
 */
export type TradingPlanPriority = 'high' | 'medium' | 'low';
/**
 * Trading Plan Item
 *
 * Individual item in the daily trading plan.
 */
export interface TradingPlanItem {
    /** The item ID */
    id: string;
    /** The item description */
    description: string;
    /** The item priority */
    priority: TradingPlanPriority;
    /** Whether the item is completed */
    completed?: boolean;
}
/**
 * Risk Management
 *
 * Risk management parameters for trading.
 */
export interface RiskManagement {
    /** The maximum risk per trade (percentage) */
    maxRiskPerTrade: number;
    /** The maximum daily loss (dollar amount) */
    maxDailyLoss: number;
    /** The maximum number of trades per day */
    maxTrades: number;
    /** The position sizing strategy */
    positionSizing: string;
}
/**
 * Trading Plan
 *
 * Complete trading plan for the day.
 */
export interface TradingPlan {
    /** The trading plan items */
    items: TradingPlanItem[];
    /** The overall strategy for the day */
    strategy: string;
    /** The risk management plan */
    riskManagement: RiskManagement;
    /** Notes for the day */
    notes: string;
}
/**
 * Default Risk Management Settings
 *
 * Provides sensible defaults for risk management.
 */
export declare const DEFAULT_RISK_MANAGEMENT: RiskManagement;
/**
 * Trading Plan Validation
 *
 * Validates a trading plan for completeness.
 */
export declare function validateTradingPlan(plan: Partial<TradingPlan>): string[];
/**
 * Trading Plan Item Factory
 *
 * Creates a new trading plan item with defaults.
 */
export declare function createTradingPlanItem(description: string, priority?: TradingPlanPriority): TradingPlanItem;
/**
 * Risk Management Calculator
 *
 * Calculates position size based on risk management rules.
 */
export declare function calculatePositionSize(accountBalance: number, entryPrice: number, stopLoss: number, riskManagement: RiskManagement): number;
//# sourceMappingURL=trading.d.ts.map