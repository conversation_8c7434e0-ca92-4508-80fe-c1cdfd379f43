import { jsx as _jsx } from "react/jsx-runtime";
import { DailyGuideProvider } from './context/DailyGuideContext';
import { DailyGuideContainer } from './components/DailyGuideContainer';
/**
 * DailyGuide Component
 *
 * PATTERN VALIDATION: Simple wrapper that uses the container pattern.
 * Demonstrates cross-component-type effectiveness.
 */
const DailyGuide = () => {
    return (_jsx(DailyGuideProvider, { children: _jsx(DailyGuideContainer, {}) }));
};
export default DailyGuide;
//# sourceMappingURL=DailyGuide.js.map