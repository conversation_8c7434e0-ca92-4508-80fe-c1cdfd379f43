/**
 * useTradingPlanForm Hook
 *
 * Custom hook for managing trading plan form state and logic.
 * Extracted from the original TradingPlan component for better separation of concerns.
 */
import { TradingPlanItem } from '../types';
export interface UseTradingPlanFormReturn {
    /** Whether the add form is currently shown */
    showAddForm: boolean;
    /** Function to show/hide the add form */
    setShowAddForm: (show: boolean) => void;
    /** The new item being created */
    newItem: Omit<TradingPlanItem, 'id'>;
    /** Function to update the new item */
    setNewItem: (item: Omit<TradingPlanItem, 'id'>) => void;
    /** Function to handle form submission */
    handleAddItem: (e: React.FormEvent) => void;
    /** Function to reset the form to initial state */
    resetForm: () => void;
    /** Whether the form is valid */
    isValid: boolean;
}
/**
 * useTradingPlanForm Hook
 *
 * Manages form state and validation for adding new trading plan items.
 * Provides a clean API for form interactions and state management.
 */
export declare const useTradingPlanForm: (onItemAdd?: (item: TradingPlanItem) => void) => UseTradingPlanFormReturn;
export default useTradingPlanForm;
//# sourceMappingURL=useTradingPlanForm.d.ts.map