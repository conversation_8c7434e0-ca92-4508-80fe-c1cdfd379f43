/**
 * Daily Guide Context
 *
 * Context for managing daily guide state
 */
import React, { ReactNode } from 'react';
import { DailyGuideState } from '../types';
interface DailyGuideContextType extends DailyGuideState {
    fetchGuideData: () => Promise<void>;
    updateTradingPlanItem: (id: string, completed: boolean) => void;
    refreshData: () => void;
}
interface DailyGuideProviderProps {
    children: ReactNode;
}
export declare const DailyGuideProvider: React.FC<DailyGuideProviderProps>;
export declare const useDailyGuide: () => DailyGuideContextType;
export {};
//# sourceMappingURL=DailyGuideContext.d.ts.map