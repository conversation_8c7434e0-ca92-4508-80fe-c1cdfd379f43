import { jsx as _jsx, jsxs as _jsxs } from 'react/jsx-runtime';
import { NavLink, useLocation } from 'react-router-dom';
import styled from 'styled-components';
// Sidebar container
const SidebarContainer = styled.aside`
  height: 100%;
  background-color: ${({ theme }) => theme.colors.surface};
  border-right: 1px solid var(--border-primary);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transition: width ${({ theme }) => theme.transitions.normal};
`;
// Logo container
const LogoContainer = styled.div`
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: ${({ isOpen }) => (isOpen ? 'flex-start' : 'center')};
  padding: 0 ${({ theme, isOpen }) => (isOpen ? theme.spacing.md : '0')};
  border-bottom: 1px solid var(--border-primary);
`;
// Logo
const Logo = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.lg};
  font-weight: bold;
  color: ${({ theme }) => theme.colors.primary};
  white-space: nowrap;
  overflow: hidden;
  opacity: ${({ isOpen }) => (isOpen ? 1 : 0)};
  transition: opacity ${({ theme }) => theme.transitions.normal};
`;
// Navigation container
const NavContainer = styled.nav`
  flex: 1;
  overflow-y: auto;
  padding: ${({ theme }) => theme.spacing.md} 0;
`;
// Navigation item
const NavItem = styled(NavLink)`
  display: flex;
  align-items: center;
  padding: ${({ theme }) => theme.spacing.sm}
    ${({ theme, $isOpen }) => ($isOpen ? theme.spacing.md : '0')};
  justify-content: ${({ $isOpen }) => ($isOpen ? 'flex-start' : 'center')};
  color: var(--text-secondary);
  text-decoration: none;
  transition: background-color ${({ theme }) => theme.transitions.fast},
    color ${({ theme }) => theme.transitions.fast};

  &:hover {
    background-color: rgba(255, 255, 255, 0.05);
    color: ${({ theme }) => theme.colors.textPrimary};
  }

  &.active {
    color: ${({ theme }) => theme.colors.primary};
    background-color: rgba(255, 255, 255, 0.05);
    border-left: 3px solid ${({ theme }) => theme.colors.primary};
  }
`;
// Icon
const Icon = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  margin-right: ${({ theme }) => theme.spacing.sm};
`;
// Label
const Label = styled.span`
  white-space: nowrap;
  opacity: ${({ isOpen }) => (isOpen ? 1 : 0)};
  transition: opacity ${({ theme }) => theme.transitions.normal};
  overflow: hidden;
  max-width: ${({ isOpen }) => (isOpen ? '200px' : '0')};
`;
// Footer
const Footer = styled.div`
  padding: ${({ theme }) => theme.spacing.sm} ${({ theme }) => theme.spacing.md};
  border-top: 1px solid ${({ theme }) => theme.colors.border};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.textSecondary};
  white-space: nowrap;
  opacity: ${({ isOpen }) => (isOpen ? 1 : 0)};
  transition: opacity ${({ theme }) => theme.transitions.normal};
  text-align: center;
`;
/**
 * Sidebar Component
 */
const Sidebar = ({ isOpen }) => {
  const location = useLocation();
  const navItems = [
    { path: '/', label: 'Dashboard', icon: '📊' },
    { path: '/daily-guide', label: 'Daily Guide', icon: '📅' },
    { path: '/journal', label: 'Trade Journal', icon: '📓' },
    { path: '/analysis', label: 'Analysis', icon: '📈' },
    { path: '/settings', label: 'Settings', icon: '⚙️' },
  ];
  return _jsxs(SidebarContainer, {
    isOpen: isOpen,
    children: [
      _jsx(LogoContainer, {
        isOpen: isOpen,
        children: _jsx(Logo, { isOpen: isOpen, children: 'ADHD' }),
      }),
      _jsx(NavContainer, {
        children: navItems.map(item =>
          _jsxs(
            NavItem,
            {
              to: item.path,
              $isOpen: isOpen,
              className: location.pathname === item.path ? 'active' : '',
              children: [
                _jsx(Icon, { children: item.icon }),
                _jsx(Label, { isOpen: isOpen, children: item.label }),
              ],
            },
            item.path
          )
        ),
      }),
      _jsx(Footer, { isOpen: isOpen, children: 'v1.0.0' }),
    ],
  });
};
export default Sidebar;
//# sourceMappingURL=Sidebar.js.map
