import { jsx as _jsx, jsxs as _jsxs } from 'react/jsx-runtime';
/**
 * Test App Component
 *
 * A minimal test component to verify React rendering
 */
import * as React from 'react';
/**
 * TestApp Component
 *
 * A minimal component for testing React rendering
 */
const TestApp = () => {
  const [count, setCount] = React.useState(0);
  React.useEffect(() => {
    console.log('TestApp mounted');
    return () => {
      console.log('TestApp unmounted');
    };
  }, []);
  const handleClick = () => {
    setCount(prev => prev + 1);
  };
  const styles = {
    container: {
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      height: '100vh',
      backgroundColor: 'var(--bg-primary)',
      color: 'white',
      fontFamily: 'Inter, sans-serif',
    },
    heading: {
      fontSize: '2rem',
      marginBottom: '1rem',
      color: 'var(--primary-color)',
    },
    button: {
      padding: '0.5rem 1rem',
      backgroundColor: 'var(--primary-color)',
      color: 'white',
      border: 'none',
      borderRadius: '0.25rem',
      cursor: 'pointer',
      fontSize: '1rem',
      marginTop: '1rem',
    },
    counter: {
      fontSize: '1.5rem',
      marginTop: '1rem',
    },
  };
  return _jsxs('div', {
    style: styles.container,
    children: [
      _jsx('h1', { style: styles.heading, children: 'ADHD Trading Dashboard Test' }),
      _jsx('p', { children: 'This is a minimal test component to verify React rendering' }),
      _jsxs('div', { style: styles.counter, children: ['Count: ', count] }),
      _jsx('button', { style: styles.button, onClick: handleClick, children: 'Increment' }),
    ],
  });
};
export default TestApp;
//# sourceMappingURL=TestApp.js.map
