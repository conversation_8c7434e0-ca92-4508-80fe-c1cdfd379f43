/**
 * Trade Form Page
 *
 * Page component that renders the TradeForm feature component.
 * This serves as a wrapper to maintain separation between pages and features.
 */
import React from 'react';
/**
 * TradeForm Page
 *
 * Renders the TradeForm feature component which handles adding or editing trades.
 * The actual implementation is in the features/trade-journal directory.
 *
 * This component also handles route parameter validation to ensure proper navigation.
 */
declare const TradeForm: React.FC;
export default TradeForm;
//# sourceMappingURL=TradeForm.d.ts.map