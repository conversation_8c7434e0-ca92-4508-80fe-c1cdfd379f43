/**
 * Unified Theme System
 * 
 * Single source of truth for all theme variables.
 * This file consolidates and replaces the fragmented CSS variable files.
 */

/* =============================================================================
   ROOT DEFAULTS (Fallback values)
   ============================================================================= */

:root {
  /* Default spacing system */
  --spacing-xxs: 4px;
  --spacing-xs: 8px;
  --spacing-sm: 12px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-xxl: 48px;

  /* Default typography */
  --font-family-body: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-family-heading: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-family-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, monospace;

  /* Default shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

  /* Default border radius */
  --border-radius-xs: 2px;
  --border-radius-sm: 4px;
  --border-radius-md: 8px;
  --border-radius-lg: 12px;
  --border-radius-xl: 16px;

  /* Default transitions */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.2s ease;
  --transition-slow: 0.3s ease;

  /* Extended Typography Scale */
  --font-size-2xl: 1.875rem; /* 30px */
  --font-size-3xl: 2.25rem; /* 36px */
  --font-size-4xl: 3rem; /* 48px */
}

/* =============================================================================
   MERCEDES GREEN THEME
   ============================================================================= */

[data-theme='mercedes-green'] {
  /* Primary Colors */
  --primary-color: #00d2be;
  --primary-dark: #00a896;
  --primary-light: #00ffe5;
  --secondary-color: #0600ef;
  --accent-color: #ff8700;

  /* Status Colors */
  --success-color: #00d2be;
  --warning-color: #ff8700;
  --error-color: #e10600;
  --info-color: #0600ef;

  /* Background Colors */
  --bg-primary: #1a1a1a;
  --bg-secondary: #1a1f2c;
  --bg-card: #242936;
  --bg-elevated: #2a2f3c;
  --bg-surface: #242936;

  /* Text Colors */
  --text-primary: #ffffff;
  --text-secondary: #b0b7c3;
  --text-disabled: #7a8194;
  --text-inverse: #1a1a1a;

  /* Border Colors */
  --border-primary: #3a3f4c;
  --border-secondary: rgba(255, 255, 255, 0.1);

  /* Session States */
  --session-active: #00d2be;
  --session-optimal: #00ffe5;
  --session-caution: #ffd320;
  --session-transition: #0600ef;
  --session-inactive: #9ca3af;

  /* Performance States */
  --performance-excellent: #00ffe5;
  --performance-good: #00d2be;
  --performance-average: #ffd320;
  --performance-poor: #ff8700;
  --performance-avoid: #e10600;

  /* Component Semantic Variables */
  --session-card-bg: var(--bg-card);
  --session-card-border: var(--border-primary);
  --session-card-accent: var(--primary-color);
  --session-text-primary: var(--text-primary);
  --session-text-secondary: var(--text-secondary);

  /* Elite Intelligence Semantic Variables */
  --elite-card-bg: #1a1a1a;
  --elite-card-border: #333333;
  --elite-card-accent-stripe: var(--primary-color);
  --elite-glass-bg: rgba(255, 255, 255, 0.03);
  --elite-glass-border: rgba(255, 255, 255, 0.1);
  --elite-section-bg: rgba(42, 42, 42, 0.6);
}

/* =============================================================================
   F1 OFFICIAL THEME
   ============================================================================= */

[data-theme='f1-official'] {
  /* Primary Colors */
  --primary-color: #e10600;
  --primary-dark: #b30500;
  --primary-light: #ff1e1e;
  --secondary-color: #15151e;
  --accent-color: #ffd700;

  /* Status Colors */
  --success-color: #00ff41;
  --warning-color: #ffd700;
  --error-color: #ff1e1e;
  --info-color: #00b4d8;

  /* Background Colors */
  --bg-primary: #15151e;
  --bg-secondary: #1e1e2e;
  --bg-card: #2a2a3a;
  --bg-elevated: #353545;
  --bg-surface: #1e1e2e;

  /* Text Colors */
  --text-primary: #ffffff;
  --text-secondary: #b8b8c8;
  --text-disabled: #8b8b9b;
  --text-inverse: #15151e;

  /* Border Colors */
  --border-primary: #3a3a4a;
  --border-secondary: #4a4a5a;

  /* Session States */
  --session-active: #e10600;
  --session-optimal: #ffd700;
  --session-caution: #ff8700;
  --session-transition: #00b4d8;
  --session-inactive: #8b8b9b;

  /* Performance States */
  --performance-excellent: #00ff41;
  --performance-good: #00b4d8;
  --performance-average: #ffd700;
  --performance-poor: #ff8700;
  --performance-avoid: #ff1e1e;

  /* Component Semantic Variables */
  --session-card-bg: var(--bg-card);
  --session-card-border: var(--border-primary);
  --session-card-accent: var(--primary-color);
  --session-text-primary: var(--text-primary);
  --session-text-secondary: var(--text-secondary);

  /* Elite Intelligence Semantic Variables */
  --elite-card-bg: #2a2a3a;
  --elite-card-border: #3a3a4a;
  --elite-card-accent-stripe: var(--primary-color);
  --elite-glass-bg: rgba(255, 255, 255, 0.05);
  --elite-glass-border: rgba(255, 255, 255, 0.1);
  --elite-section-bg: rgba(53, 53, 69, 0.6);
}

/* =============================================================================
   DARK THEME
   ============================================================================= */

[data-theme='dark'] {
  /* Primary Colors */
  --primary-color: #6366f1;
  --primary-dark: #4f46e5;
  --primary-light: #818cf8;
  --secondary-color: #4f46e5;
  --accent-color: #10b981;

  /* Status Colors */
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --info-color: #3b82f6;

  /* Background Colors */
  --bg-primary: #111827;
  --bg-secondary: #1f2937;
  --bg-card: #374151;
  --bg-elevated: #4b5563;
  --bg-surface: #1f2937;

  /* Text Colors */
  --text-primary: #ffffff;
  --text-secondary: #e5e7eb;
  --text-disabled: #9ca3af;
  --text-inverse: #111827;

  /* Border Colors */
  --border-primary: #4b5563;
  --border-secondary: rgba(255, 255, 255, 0.1);

  /* Session States */
  --session-active: #6366f1;
  --session-optimal: #10b981;
  --session-caution: #f59e0b;
  --session-transition: #3b82f6;
  --session-inactive: #9ca3af;

  /* Performance States */
  --performance-excellent: #10b981;
  --performance-good: #3b82f6;
  --performance-average: #f59e0b;
  --performance-poor: #f97316;
  --performance-avoid: #ef4444;

  /* Component Semantic Variables */
  --session-card-bg: var(--bg-card);
  --session-card-border: var(--border-primary);
  --session-card-accent: var(--primary-color);
  --session-text-primary: var(--text-primary);
  --session-text-secondary: var(--text-secondary);

  /* Elite Intelligence Semantic Variables */
  --elite-card-bg: #374151;
  --elite-card-border: #4b5563;
  --elite-card-accent-stripe: var(--primary-color);
  --elite-glass-bg: rgba(255, 255, 255, 0.05);
  --elite-glass-border: rgba(255, 255, 255, 0.1);
  --elite-section-bg: rgba(75, 85, 99, 0.6);
}
