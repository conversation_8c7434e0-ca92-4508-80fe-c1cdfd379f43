/**
 * Detailed Analysis Panel Component
 *
 * Expandable sections showing comprehensive trading intelligence.
 * Organized into logical sections with progressive disclosure to prevent
 * information overload while providing access to detailed analysis.
 */

import React, { useState } from 'react';
import styled from 'styled-components';
import { SetupIntelligenceData } from '../hooks/useEnhancedSetupIntelligence';
import { ModelRecommendation } from '../hooks/useModelSelectionEngine';
import { PatternQualityScore } from '../hooks/usePatternQualityScoring';
import { SuccessProbability } from '../hooks/useSuccessProbabilityCalculator';

export interface DetailedAnalysisPanelProps {
  /** Model recommendation data */
  modelRecommendation: ModelRecommendation;
  /** Pattern quality analysis */
  patternQuality: PatternQualityScore;
  /** Success probability calculation */
  successProbability: SuccessProbability;
  /** Setup intelligence data */
  setupIntelligence: SetupIntelligenceData;
  /** Whether data is loading */
  isLoading?: boolean;
  /** Error state */
  error?: string | null;
}

// Styled components
const PanelContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

const ExpandableSection = styled.div`
  background: var(--card-bg);
  border: 1px solid var(--card-border);
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
`;

const SectionHeader = styled.button<{ $isExpanded: boolean }>`
  width: 100%;
  background: var(--surface-bg);
  border: none;
  padding: 16px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: var(--hover-bg);
    transform: translateY(-1px);
  }

  border-bottom: ${({ $isExpanded }) => ($isExpanded ? '1px solid var(--border-color)' : 'none')};
`;

const SectionTitle = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
`;

const SectionSummary = styled.div`
  font-size: 12px;
  color: var(--text-secondary);
  margin-top: 2px;
`;

const ExpandIcon = styled.span<{ $isExpanded: boolean }>`
  font-size: 14px;
  transition: transform 0.2s ease;
  transform: ${({ $isExpanded }) => ($isExpanded ? 'rotate(180deg)' : 'rotate(0deg)')};
`;

const SectionContent = styled.div<{ $isExpanded: boolean }>`
  max-height: ${({ $isExpanded }) => ($isExpanded ? '1000px' : '0')};
  overflow: hidden;
  transition: max-height 0.3s ease;
  padding: ${({ $isExpanded }) => ($isExpanded ? '20px' : '0 20px')};
`;

const DetailGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
`;

const DetailItem = styled.div`
  background: var(--surface-bg);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 16px;
  transition: all 0.2s ease;

  &:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
`;

const DetailLabel = styled.div`
  font-size: 12px;
  font-weight: 500;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 6px;
`;

const DetailValue = styled.div`
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 4px;
`;

const DetailDescription = styled.div`
  font-size: 12px;
  color: var(--text-muted);
  line-height: 1.4;
`;

/**
 * Detailed Analysis Panel Component
 */
export const DetailedAnalysisPanel: React.FC<DetailedAnalysisPanelProps> = ({
  modelRecommendation,
  patternQuality,
  successProbability,
  isLoading = false,
  error = null,
}) => {
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set(['model']));

  const toggleSection = (sectionId: string) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(sectionId)) {
      newExpanded.delete(sectionId);
    } else {
      newExpanded.add(sectionId);
    }
    setExpandedSections(newExpanded);
  };

  if (isLoading) {
    return (
      <PanelContainer>
        <div style={{ textAlign: 'center', padding: '40px', color: 'var(--text-secondary)' }}>
          Loading detailed analysis...
        </div>
      </PanelContainer>
    );
  }

  if (error) {
    return (
      <PanelContainer>
        <div style={{ textAlign: 'center', padding: '40px', color: 'var(--error-text)' }}>
          Error loading detailed analysis: {error}
        </div>
      </PanelContainer>
    );
  }

  const isExpanded = (sectionId: string) => expandedSections.has(sectionId);

  return (
    <PanelContainer>
      {/* Model Selection Analysis */}
      <ExpandableSection>
        <SectionHeader $isExpanded={isExpanded('model')} onClick={() => toggleSection('model')}>
          <div>
            <SectionTitle>🎯 Model Selection Analysis</SectionTitle>
            <SectionSummary>
              {modelRecommendation.recommendedModel} • {modelRecommendation.probability.toFixed(0)}%
              confidence
            </SectionSummary>
          </div>
          <ExpandIcon $isExpanded={isExpanded('model')}>▼</ExpandIcon>
        </SectionHeader>

        <SectionContent $isExpanded={isExpanded('model')}>
          <DetailGrid>
            <DetailItem>
              <DetailLabel>Recommended Model</DetailLabel>
              <DetailValue>{modelRecommendation.recommendedModel}</DetailValue>
              <DetailDescription>{modelRecommendation.confidence} confidence</DetailDescription>
            </DetailItem>

            <DetailItem>
              <DetailLabel>Alternative Model</DetailLabel>
              <DetailValue>{modelRecommendation.alternativeModel}</DetailValue>
              <DetailDescription>{modelRecommendation.alternativeCondition}</DetailDescription>
            </DetailItem>

            <DetailItem>
              <DetailLabel>Market Volatility</DetailLabel>
              <DetailValue>
                {modelRecommendation.marketConditions.volatility.toUpperCase()}
              </DetailValue>
              <DetailDescription>Current market volatility assessment</DetailDescription>
            </DetailItem>

            <DetailItem>
              <DetailLabel>HTF Trend</DetailLabel>
              <DetailValue>
                {modelRecommendation.marketConditions.htfTrend.toUpperCase()}
              </DetailValue>
              <DetailDescription>Higher timeframe trend direction</DetailDescription>
            </DetailItem>
          </DetailGrid>

          <DetailDescription
            style={{ padding: '12px', background: 'var(--surface-bg)', borderRadius: '8px' }}
          >
            <strong>Reasoning:</strong> {modelRecommendation.reasoning}
          </DetailDescription>
        </SectionContent>
      </ExpandableSection>

      {/* Pattern Quality Analysis */}
      <ExpandableSection>
        <SectionHeader $isExpanded={isExpanded('pattern')} onClick={() => toggleSection('pattern')}>
          <div>
            <SectionTitle>📊 Pattern Quality Analysis</SectionTitle>
            <SectionSummary>
              {patternQuality.totalScore.toFixed(1)}/5.0 • {patternQuality.rating}
            </SectionSummary>
          </div>
          <ExpandIcon $isExpanded={isExpanded('pattern')}>▼</ExpandIcon>
        </SectionHeader>

        <SectionContent $isExpanded={isExpanded('pattern')}>
          <DetailGrid>
            <DetailItem>
              <DetailLabel>PD Array Confluence</DetailLabel>
              <DetailValue>{patternQuality.breakdown.pdArrayConfluence.toFixed(1)}</DetailValue>
              <DetailDescription>Multiple PD array alignment</DetailDescription>
            </DetailItem>

            <DetailItem>
              <DetailLabel>FVG Characteristics</DetailLabel>
              <DetailValue>{patternQuality.breakdown.fvgCharacteristics.toFixed(1)}</DetailValue>
              <DetailDescription>Fair value gap quality</DetailDescription>
            </DetailItem>

            <DetailItem>
              <DetailLabel>RD Strength</DetailLabel>
              <DetailValue>{patternQuality.breakdown.rdStrength.toFixed(1)}</DetailValue>
              <DetailDescription>Reaction/displacement power</DetailDescription>
            </DetailItem>

            <DetailItem>
              <DetailLabel>Confirmation Signals</DetailLabel>
              <DetailValue>{patternQuality.breakdown.confirmationSignals.toFixed(1)}</DetailValue>
              <DetailDescription>Supporting technical signals</DetailDescription>
            </DetailItem>
          </DetailGrid>

          <DetailDescription
            style={{ padding: '12px', background: 'var(--surface-bg)', borderRadius: '8px' }}
          >
            <strong>Recommendation:</strong> {patternQuality.recommendation}
          </DetailDescription>
        </SectionContent>
      </ExpandableSection>

      {/* Success Probability Analysis */}
      <ExpandableSection>
        <SectionHeader
          $isExpanded={isExpanded('probability')}
          onClick={() => toggleSection('probability')}
        >
          <div>
            <SectionTitle>🎯 Success Probability Analysis</SectionTitle>
            <SectionSummary>
              {successProbability.finalProbability.toFixed(0)}% •{' '}
              {successProbability.recommendation.replace('_', ' ')}
            </SectionSummary>
          </div>
          <ExpandIcon $isExpanded={isExpanded('probability')}>▼</ExpandIcon>
        </SectionHeader>

        <SectionContent $isExpanded={isExpanded('probability')}>
          <DetailGrid>
            <DetailItem>
              <DetailLabel>Base Model Win Rate</DetailLabel>
              <DetailValue>{successProbability.breakdown.baseModelWinRate.toFixed(0)}%</DetailValue>
              <DetailDescription>Historical model performance</DetailDescription>
            </DetailItem>

            <DetailItem>
              <DetailLabel>Session Bonus</DetailLabel>
              <DetailValue>
                {successProbability.breakdown.sessionBonus > 0 ? '+' : ''}
                {successProbability.breakdown.sessionBonus.toFixed(0)}%
              </DetailValue>
              <DetailDescription>Current session performance boost</DetailDescription>
            </DetailItem>

            <DetailItem>
              <DetailLabel>Quality Bonus</DetailLabel>
              <DetailValue>
                {successProbability.breakdown.qualityBonus > 0 ? '+' : ''}
                {successProbability.breakdown.qualityBonus.toFixed(0)}%
              </DetailValue>
              <DetailDescription>Pattern quality enhancement</DetailDescription>
            </DetailItem>

            <DetailItem>
              <DetailLabel>Expected R-Multiple</DetailLabel>
              <DetailValue>
                {successProbability.expectedRMultiple.min.toFixed(1)} -{' '}
                {successProbability.expectedRMultiple.max.toFixed(1)}
              </DetailValue>
              <DetailDescription>
                Risk-reward range (avg: {successProbability.expectedRMultiple.average.toFixed(1)})
              </DetailDescription>
            </DetailItem>
          </DetailGrid>
        </SectionContent>
      </ExpandableSection>
    </PanelContainer>
  );
};

export default DetailedAnalysisPanel;
