/**
 * Quick Decision Panel Component
 *
 * ADHD-optimized summary view showing only the essential information
 * needed for immediate trading decisions. Eliminates information overload
 * by focusing on actionable insights.
 */

import React from 'react';
import styled from 'styled-components';
import { SetupIntelligenceData } from '../hooks/useEnhancedSetupIntelligence';
import { ModelRecommendation } from '../hooks/useModelSelectionEngine';
import { PatternQualityScore } from '../hooks/usePatternQualityScoring';
import { SuccessProbability } from '../hooks/useSuccessProbabilityCalculator';
import { MarketStateIndicator, getCurrentMarketState } from './MarketStateIndicator';

export interface QuickDecisionPanelProps {
  /** Model recommendation data */
  modelRecommendation: ModelRecommendation;
  /** Pattern quality analysis */
  patternQuality: PatternQualityScore;
  /** Success probability calculation */
  successProbability: SuccessProbability;
  /** Setup intelligence data */
  setupIntelligence: SetupIntelligenceData;
  /** Whether data is loading */
  isLoading?: boolean;
  /** Error state */
  error?: string | null;
  /** Callback to expand to detailed view */
  onExpandDetails?: () => void;
}

// Styled components
const PanelContainer = styled.div`
  background: linear-gradient(135deg, var(--surface-bg) 0%, var(--card-bg) 100%);
  border: 2px solid var(--primary-color);
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
`;

const PanelHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--border-color);
`;

const Title = styled.h3`
  margin: 0;
  font-size: 24px;
  font-weight: 700;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 8px;
`;

const ExpandButton = styled.button`
  background: var(--primary-color);
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 12px;
  font-weight: 600;
  color: var(--primary-contrast);
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
  }
`;

const QuickGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 24px;
  margin-bottom: 20px;
  align-items: center;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 16px;
  }
`;

const MarketContextBanner = styled.div<{ $isWeekend: boolean }>`
  background: ${({ $isWeekend }) =>
    $isWeekend
      ? 'var(--warning-bg, rgba(251, 191, 36, 0.1))'
      : 'var(--info-bg, rgba(59, 130, 246, 0.1))'};
  border: 1px solid
    ${({ $isWeekend }) =>
      $isWeekend
        ? 'var(--warning-border, rgba(251, 191, 36, 0.3))'
        : 'var(--info-border, rgba(59, 130, 246, 0.3))'};
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 20px;
  font-size: 13px;
  font-weight: 500;
  color: var(--text-secondary);
  text-align: center;
`;

const PrimaryRecommendation = styled.div`
  text-align: center;
  padding: 16px;
`;

const ModelName = styled.div`
  font-size: 32px;
  font-weight: 900;
  color: var(--primary-color);
  margin-bottom: 8px;
  line-height: 1;
`;

const ConfidenceBadge = styled.span<{ $confidence: string }>`
  background: var(--primary-color);
  color: var(--primary-contrast);
  padding: 4px 12px;
  border-radius: 20px;
  font-weight: 700;
  font-size: 14px;
  text-transform: uppercase;
`;

const SetupRecommendation = styled.div`
  padding: 16px;
  background: rgba(var(--primary-rgb), 0.1);
  border-radius: 8px;
  border-left: 4px solid var(--primary-color);
`;

const SetupTitle = styled.div`
  font-size: 14px;
  color: var(--primary-color);
  margin-bottom: 8px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;

const SetupDescription = styled.div`
  font-size: 16px;
  line-height: 1.4;
  color: var(--text-primary);
  margin-bottom: 8px;
`;

const SetupStats = styled.div`
  font-size: 12px;
  color: var(--primary-color);
  font-weight: 600;
`;

const ProbabilitySection = styled.div`
  text-align: center;
  padding: 16px;
`;

const ProbabilityValue = styled.div<{ $probability: number }>`
  font-size: 48px;
  font-weight: 900;
  line-height: 1;
  color: ${({ $probability }) => {
    if ($probability >= 70) return 'var(--success-color)';
    if ($probability >= 50) return 'var(--warning-color)';
    return 'var(--error-color)';
  }};
`;

const ProbabilityLabel = styled.div`
  font-size: 14px;
  color: var(--text-secondary);
  margin-top: 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;

const QualityAlert = styled.div<{ $show: boolean }>`
  display: ${({ $show }) => ($show ? 'block' : 'none')};
  background: var(--error-bg);
  color: var(--error-text);
  padding: 12px 16px;
  border-radius: 8px;
  margin: 16px 0;
  text-align: center;
  font-weight: 600;
  border: 1px solid var(--error-border);
`;

const ReasoningBox = styled.div`
  background: rgba(var(--primary-rgb), 0.05);
  border: 1px solid rgba(var(--primary-rgb), 0.2);
  border-radius: 8px;
  padding: 16px;
  margin-top: 16px;
`;

const ReasoningTitle = styled.div`
  color: var(--primary-color);
  font-weight: 600;
  margin-bottom: 8px;
  font-size: 14px;
`;

/**
 * Quick Decision Panel Component
 */
export const QuickDecisionPanel: React.FC<QuickDecisionPanelProps> = ({
  modelRecommendation,
  patternQuality,
  successProbability,
  setupIntelligence,
  isLoading = false,
  error = null,
  onExpandDetails,
}) => {
  const marketState = getCurrentMarketState();
  const isWeekend = marketState.dayOfWeek === 'Saturday' || marketState.dayOfWeek === 'Sunday';
  const showContextBanner = !marketState.isOpen;

  if (isLoading) {
    return (
      <PanelContainer>
        <div style={{ textAlign: 'center', padding: '20px', color: 'var(--text-secondary)' }}>
          Analyzing market conditions...
        </div>
      </PanelContainer>
    );
  }

  if (error) {
    return (
      <PanelContainer>
        <div style={{ textAlign: 'center', padding: '20px', color: 'var(--error-text)' }}>
          Error loading intelligence data
        </div>
      </PanelContainer>
    );
  }

  return (
    <PanelContainer>
      <PanelHeader>
        <Title>🎯 Quick Decision</Title>
        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
          <MarketStateIndicator marketState={marketState} />
          {onExpandDetails && (
            <ExpandButton onClick={onExpandDetails}>📊 Detailed Analysis</ExpandButton>
          )}
        </div>
      </PanelHeader>

      {showContextBanner && (
        <MarketContextBanner $isWeekend={isWeekend}>
          {isWeekend
            ? '📅 Weekend Analysis - Using most recent trading session data for pattern evaluation'
            : `⏰ ${marketState.status.replace('_', ' ')} - Analysis based on ${
                marketState.status === 'PRE_MARKET' ? 'previous session' : 'current session'
              } data`}
        </MarketContextBanner>
      )}

      <QuickGrid>
        <PrimaryRecommendation>
          <ModelName>{modelRecommendation.recommendedModel}</ModelName>
          <ConfidenceBadge $confidence={modelRecommendation.confidence}>
            {modelRecommendation.probability.toFixed(0)}% {modelRecommendation.confidence}
          </ConfidenceBadge>
        </PrimaryRecommendation>

        <SetupRecommendation>
          <SetupTitle>Recommended Setup</SetupTitle>
          <SetupDescription>
            {setupIntelligence.currentRecommendations.primarySetup} +{' '}
            {setupIntelligence.currentRecommendations.secondarySetup}
          </SetupDescription>
          <SetupStats>
            {setupIntelligence.currentRecommendations.expectedWinRate.toFixed(0)}% Win Rate |{' '}
            {setupIntelligence.currentRecommendations.expectedRMultiple.toFixed(1)}R Avg
          </SetupStats>
        </SetupRecommendation>

        <ProbabilitySection>
          <ProbabilityValue $probability={successProbability.finalProbability}>
            {successProbability.finalProbability.toFixed(0)}%
          </ProbabilityValue>
          <ProbabilityLabel>Success Probability</ProbabilityLabel>
        </ProbabilitySection>
      </QuickGrid>

      <QualityAlert $show={patternQuality.totalScore < 2.0}>
        ⚠️ Current Pattern Quality: {patternQuality.rating} ({patternQuality.totalScore.toFixed(1)}
        /5.0) - {patternQuality.recommendation}
      </QualityAlert>

      <ReasoningBox>
        <ReasoningTitle>Why {modelRecommendation.recommendedModel}?</ReasoningTitle>
        <div style={{ fontSize: '14px', lineHeight: '1.4', color: 'var(--text-secondary)' }}>
          {modelRecommendation.reasoning}
        </div>
      </ReasoningBox>
    </PanelContainer>
  );
};

export default QuickDecisionPanel;
