/**
 * Quick Decision Panel Component
 *
 * ADHD-optimized summary view showing only the essential information
 * needed for immediate trading decisions. Eliminates information overload
 * by focusing on actionable insights.
 */

import React from 'react';
import styled from 'styled-components';
import { SetupIntelligenceData } from '../hooks/useEnhancedSetupIntelligence';
import { ModelRecommendation } from '../hooks/useModelSelectionEngine';
import { PatternQualityScore } from '../hooks/usePatternQualityScoring';
import { SuccessProbability } from '../hooks/useSuccessProbabilityCalculator';
import { MarketStateIndicator, getCurrentMarketState } from './MarketStateIndicator';

export interface QuickDecisionPanelProps {
  /** Model recommendation data */
  modelRecommendation: ModelRecommendation;
  /** Pattern quality analysis */
  patternQuality: PatternQualityScore;
  /** Success probability calculation */
  successProbability: SuccessProbability;
  /** Setup intelligence data */
  setupIntelligence: SetupIntelligenceData;
  /** Whether data is loading */
  isLoading?: boolean;
  /** Error state */
  error?: string | null;
  /** Callback to expand to detailed view */
  onExpandDetails?: () => void;
}

// Styled components
const PanelContainer = styled.div`
  background: var(--card-bg);
  border: 1px solid var(--card-border);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
`;

const PanelHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
`;

const Title = styled.h3`
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 8px;
`;

const ExpandButton = styled.button`
  background: none;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  padding: 6px 12px;
  font-size: 12px;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: var(--hover-bg);
    color: var(--text-primary);
    border-color: var(--primary-color);
  }
`;

const QuickGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 16px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;

const MetricCard = styled.div<{ $priority?: 'high' | 'medium' | 'low' }>`
  background: ${({ $priority }) => {
    switch ($priority) {
      case 'high':
        return 'var(--success-bg, rgba(34, 197, 94, 0.1))';
      case 'medium':
        return 'var(--warning-bg, rgba(251, 191, 36, 0.1))';
      case 'low':
        return 'var(--error-bg, rgba(239, 68, 68, 0.1))';
      default:
        return 'var(--surface-bg)';
    }
  }};
  border: 1px solid
    ${({ $priority }) => {
      switch ($priority) {
        case 'high':
          return 'var(--success-border, rgba(34, 197, 94, 0.3))';
        case 'medium':
          return 'var(--warning-border, rgba(251, 191, 36, 0.3))';
        case 'low':
          return 'var(--error-border, rgba(239, 68, 68, 0.3))';
        default:
          return 'var(--border-color)';
      }
    }};
  border-radius: 8px;
  padding: 12px;
`;

const MetricLabel = styled.div`
  font-size: 11px;
  font-weight: 500;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 4px;
`;

const MetricValue = styled.div`
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 2px;
`;

const MetricSubtext = styled.div`
  font-size: 10px;
  color: var(--text-muted);
`;

const ActionRecommendation = styled.div<{ $recommendation: string }>`
  background: ${({ $recommendation }) => {
    if ($recommendation.includes('PRIORITIZE')) return 'var(--success-bg, rgba(34, 197, 94, 0.1))';
    if ($recommendation.includes('AVOID')) return 'var(--error-bg, rgba(239, 68, 68, 0.1))';
    return 'var(--warning-bg, rgba(251, 191, 36, 0.1))';
  }};
  border: 1px solid
    ${({ $recommendation }) => {
      if ($recommendation.includes('PRIORITIZE'))
        return 'var(--success-border, rgba(34, 197, 94, 0.3))';
      if ($recommendation.includes('AVOID')) return 'var(--error-border, rgba(239, 68, 68, 0.3))';
      return 'var(--warning-border, rgba(251, 191, 36, 0.3))';
    }};
  border-radius: 8px;
  padding: 12px;
  text-align: center;
`;

const ActionText = styled.div`
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 4px;
`;

const ActionSubtext = styled.div`
  font-size: 12px;
  color: var(--text-secondary);
`;

const MarketContextBanner = styled.div<{ $isWeekend: boolean }>`
  background: ${({ $isWeekend }) =>
    $isWeekend
      ? 'var(--warning-bg, rgba(251, 191, 36, 0.1))'
      : 'var(--info-bg, rgba(59, 130, 246, 0.1))'};
  border: 1px solid
    ${({ $isWeekend }) =>
      $isWeekend
        ? 'var(--warning-border, rgba(251, 191, 36, 0.3))'
        : 'var(--info-border, rgba(59, 130, 246, 0.3))'};
  border-radius: 8px;
  padding: 8px 12px;
  margin-bottom: 16px;
  font-size: 12px;
  color: var(--text-secondary);
  text-align: center;
`;

/**
 * Determine priority level based on success probability and confidence
 */
const getPriorityLevel = (
  successProbability: SuccessProbability,
  patternQuality: PatternQualityScore
): 'high' | 'medium' | 'low' => {
  if (successProbability.finalProbability >= 70 && patternQuality.totalScore >= 3.5) {
    return 'high';
  } else if (successProbability.finalProbability >= 60 && patternQuality.totalScore >= 2.5) {
    return 'medium';
  } else {
    return 'low';
  }
};

/**
 * Quick Decision Panel Component
 */
export const QuickDecisionPanel: React.FC<QuickDecisionPanelProps> = ({
  modelRecommendation,
  patternQuality,
  successProbability,
  setupIntelligence,
  isLoading = false,
  error = null,
  onExpandDetails,
}) => {
  const marketState = getCurrentMarketState();
  const priority = getPriorityLevel(successProbability, patternQuality);
  const isWeekend = marketState.dayOfWeek === 'Saturday' || marketState.dayOfWeek === 'Sunday';
  const showContextBanner = !marketState.isOpen;

  if (isLoading) {
    return (
      <PanelContainer>
        <div style={{ textAlign: 'center', padding: '20px', color: 'var(--text-secondary)' }}>
          Analyzing market conditions...
        </div>
      </PanelContainer>
    );
  }

  if (error) {
    return (
      <PanelContainer>
        <div style={{ textAlign: 'center', padding: '20px', color: 'var(--error-text)' }}>
          Error loading intelligence data
        </div>
      </PanelContainer>
    );
  }

  return (
    <PanelContainer>
      <PanelHeader>
        <Title>🎯 Quick Decision</Title>
        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
          <MarketStateIndicator marketState={marketState} />
          {onExpandDetails && (
            <ExpandButton onClick={onExpandDetails}>📊 Detailed Analysis</ExpandButton>
          )}
        </div>
      </PanelHeader>

      {showContextBanner && (
        <MarketContextBanner $isWeekend={isWeekend}>
          {isWeekend
            ? '📅 Weekend Analysis - Using most recent trading session data for pattern evaluation'
            : `⏰ ${marketState.status.replace('_', ' ')} - Analysis based on ${
                marketState.status === 'PRE_MARKET' ? 'previous session' : 'current session'
              } data`}
        </MarketContextBanner>
      )}

      <QuickGrid>
        <MetricCard $priority={priority}>
          <MetricLabel>Recommended Model</MetricLabel>
          <MetricValue>{modelRecommendation.recommendedModel}</MetricValue>
          <MetricSubtext>{modelRecommendation.confidence} confidence</MetricSubtext>
        </MetricCard>

        <MetricCard $priority={priority}>
          <MetricLabel>Pattern Quality</MetricLabel>
          <MetricValue>{patternQuality.totalScore.toFixed(1)}/5.0</MetricValue>
          <MetricSubtext>{patternQuality.rating}</MetricSubtext>
        </MetricCard>

        <MetricCard $priority={priority}>
          <MetricLabel>Success Probability</MetricLabel>
          <MetricValue>{successProbability.finalProbability.toFixed(0)}%</MetricValue>
          <MetricSubtext>{successProbability.confidence} confidence</MetricSubtext>
        </MetricCard>

        <MetricCard $priority={priority}>
          <MetricLabel>Position Sizing</MetricLabel>
          <MetricValue>{successProbability.riskManagement.positionSizing}</MetricValue>
          <MetricSubtext>
            {successProbability.riskManagement.maxRiskPercent}% max risk
          </MetricSubtext>
        </MetricCard>
      </QuickGrid>

      <ActionRecommendation $recommendation={successProbability.recommendation}>
        <ActionText>{successProbability.recommendation.replace('_', ' ')}</ActionText>
        <ActionSubtext>
          {setupIntelligence.currentRecommendations.primarySetup} +{' '}
          {setupIntelligence.currentRecommendations.secondarySetup}
        </ActionSubtext>
      </ActionRecommendation>
    </PanelContainer>
  );
};

export default QuickDecisionPanel;
