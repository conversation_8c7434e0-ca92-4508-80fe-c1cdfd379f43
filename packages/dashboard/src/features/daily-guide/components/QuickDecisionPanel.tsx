/**
 * Quick Decision Panel Component
 *
 * ADHD-optimized summary view showing only the essential information
 * needed for immediate trading decisions. Eliminates information overload
 * by focusing on actionable insights.
 */

import React from 'react';
import styled from 'styled-components';
import { SetupIntelligenceData } from '../hooks/useEnhancedSetupIntelligence';
import { ModelRecommendation } from '../hooks/useModelSelectionEngine';
import { PatternQualityScore } from '../hooks/usePatternQualityScoring';
import { SuccessProbability } from '../hooks/useSuccessProbabilityCalculator';
import { MarketStateIndicator, getCurrentMarketState } from './MarketStateIndicator';

export interface QuickDecisionPanelProps {
  /** Model recommendation data */
  modelRecommendation: ModelRecommendation;
  /** Pattern quality analysis */
  patternQuality: PatternQualityScore;
  /** Success probability calculation */
  successProbability: SuccessProbability;
  /** Setup intelligence data */
  setupIntelligence: SetupIntelligenceData;
  /** Whether data is loading */
  isLoading?: boolean;
  /** Error state */
  error?: string | null;
  /** Callback to expand to detailed view */
  onExpandDetails?: () => void;
}

// Styled components
const PanelContainer = styled.div`
  background: var(--elite-card-bg);
  border: 1px solid var(--elite-card-border);
  border-radius: var(--spacing-sm);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  box-shadow: var(--shadow-md);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--elite-card-accent-stripe);
  }
`;

const PanelHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
`;

const Title = styled.h3`
  margin: 0;
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
`;

const QuickGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
  align-items: stretch;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
`;

const MarketContextBanner = styled.div<{ $isWeekend: boolean }>`
  background: ${({ $isWeekend }) =>
    $isWeekend
      ? 'linear-gradient(135deg, var(--warning-color) 0%, var(--warning-dark) 100%)'
      : 'linear-gradient(135deg, var(--info-color) 0%, var(--info-dark) 100%)'};
  border: 2px solid
    ${({ $isWeekend }) => ($isWeekend ? 'var(--warning-color)' : 'var(--info-color)')};
  border-radius: var(--spacing-xs);
  padding: var(--spacing-md) var(--spacing-lg);
  margin-bottom: var(--spacing-md);
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--text-primary);
  text-align: center;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
`;

const PrimaryRecommendation = styled.div`
  text-align: center;
  padding: var(--spacing-lg);
  background: var(--elite-section-bg);
  border-radius: var(--spacing-xs);
  border: 1px solid var(--elite-card-border);
`;

const ModelName = styled.div`
  font-size: var(--font-size-4xl);
  font-weight: 900;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
  line-height: 1;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
  letter-spacing: -0.02em;
`;

const ConfidenceBadge = styled.span<{ $confidence: string }>`
  background: var(--accent-color);
  color: var(--text-primary);
  padding: var(--spacing-xxs) var(--spacing-sm);
  border-radius: 20px;
  font-weight: 700;
  font-size: var(--font-size-xs);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
`;

const SetupRecommendation = styled.div`
  padding: var(--spacing-lg);
  background: var(--elite-section-bg);
  border-radius: var(--spacing-xs);
  border: 1px solid var(--elite-card-border);
  border-left: 4px solid var(--primary-color);
  position: relative;
`;

const SetupTitle = styled.div`
  font-size: var(--font-size-xs);
  color: var(--accent-color);
  margin-bottom: var(--spacing-xs);
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 1px;
`;

const SetupDescription = styled.div`
  font-size: var(--font-size-md);
  line-height: 1.4;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
  font-weight: 600;
`;

const SetupStats = styled.div`
  font-size: var(--font-size-xs);
  color: var(--accent-color);
  font-weight: 600;
  letter-spacing: 0.5px;
`;

const ProbabilitySection = styled.div`
  text-align: center;
  padding: var(--spacing-lg);
  background: var(--elite-section-bg);
  border-radius: var(--spacing-xs);
  border: 1px solid var(--elite-card-border);
`;

const ProbabilityValue = styled.div<{ $probability: number }>`
  font-size: var(--font-size-4xl);
  font-weight: 900;
  line-height: 1;
  margin-bottom: var(--spacing-xxs);
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
  color: ${({ $probability }) => {
    if ($probability >= 70) return 'var(--success-color)';
    if ($probability >= 50) return 'var(--warning-color)';
    return 'var(--error-color)';
  }};
`;

const ProbabilityLabel = styled.div`
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
`;

const QualityAlert = styled.div<{ $show: boolean }>`
  display: ${({ $show }) => ($show ? 'block' : 'none')};
  background: linear-gradient(135deg, var(--error-color) 0%, var(--error-dark) 100%);
  color: var(--text-primary);
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--spacing-xs);
  margin: var(--spacing-md) 0;
  text-align: center;
  font-weight: 700;
  border: 2px solid var(--error-color);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  animation: pulse 2s infinite;

  @keyframes pulse {
    0%,
    100% {
      opacity: 1;
    }
    50% {
      opacity: 0.8;
    }
  }
`;

const ReasoningBox = styled.div`
  background: var(--elite-glass-bg);
  border: 1px solid var(--elite-glass-border);
  border-radius: var(--spacing-xs);
  padding: var(--spacing-lg);
  margin-top: var(--spacing-md);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--primary-color);
  }
`;

const ReasoningTitle = styled.div`
  color: var(--primary-color);
  font-weight: 700;
  margin-bottom: var(--spacing-xs);
  font-size: var(--font-size-sm);
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;

/**
 * Quick Decision Panel Component
 */
export const QuickDecisionPanel: React.FC<QuickDecisionPanelProps> = ({
  modelRecommendation,
  patternQuality,
  successProbability,
  setupIntelligence,
  isLoading = false,
  error = null,
}) => {
  const marketState = getCurrentMarketState();
  const isWeekend = marketState.dayOfWeek === 'Saturday' || marketState.dayOfWeek === 'Sunday';
  const showContextBanner = !marketState.isOpen;

  if (isLoading) {
    return (
      <PanelContainer>
        <div style={{ textAlign: 'center', padding: '20px', color: 'var(--text-secondary)' }}>
          Analyzing market conditions...
        </div>
      </PanelContainer>
    );
  }

  if (error) {
    return (
      <PanelContainer>
        <div style={{ textAlign: 'center', padding: '20px', color: 'var(--error-text)' }}>
          Error loading intelligence data
        </div>
      </PanelContainer>
    );
  }

  return (
    <PanelContainer>
      <PanelHeader>
        <Title>🎯 Quick Decision</Title>
        <MarketStateIndicator marketState={marketState} />
      </PanelHeader>

      {showContextBanner && (
        <MarketContextBanner $isWeekend={isWeekend}>
          {isWeekend
            ? '📅 Weekend Analysis - Using most recent trading session data for pattern evaluation'
            : `⏰ ${marketState.status.replace('_', ' ')} - Analysis based on ${
                marketState.status === 'PRE_MARKET' ? 'previous session' : 'current session'
              } data`}
        </MarketContextBanner>
      )}

      <QuickGrid>
        <PrimaryRecommendation>
          <ModelName>{modelRecommendation.recommendedModel}</ModelName>
          <ConfidenceBadge $confidence={modelRecommendation.confidence}>
            {modelRecommendation.probability.toFixed(0)}% {modelRecommendation.confidence}
          </ConfidenceBadge>
        </PrimaryRecommendation>

        <SetupRecommendation>
          <SetupTitle>Recommended Setup</SetupTitle>
          <SetupDescription>
            {setupIntelligence.currentRecommendations.primarySetup} +{' '}
            {setupIntelligence.currentRecommendations.secondarySetup}
          </SetupDescription>
          <SetupStats>
            {setupIntelligence.currentRecommendations.expectedWinRate.toFixed(0)}% Win Rate |{' '}
            {setupIntelligence.currentRecommendations.expectedRMultiple.toFixed(1)}R Avg
          </SetupStats>
        </SetupRecommendation>

        <ProbabilitySection>
          <ProbabilityValue $probability={successProbability.finalProbability}>
            {successProbability.finalProbability.toFixed(0)}%
          </ProbabilityValue>
          <ProbabilityLabel>Success Probability</ProbabilityLabel>
        </ProbabilitySection>
      </QuickGrid>

      <QualityAlert $show={patternQuality.totalScore < 2.0}>
        ⚠️ Current Pattern Quality: {patternQuality.rating} ({patternQuality.totalScore.toFixed(1)}
        /5.0) - {patternQuality.recommendation}
      </QualityAlert>

      <ReasoningBox>
        <ReasoningTitle>Why {modelRecommendation.recommendedModel}?</ReasoningTitle>
        <div
          style={{
            fontSize: 'var(--font-size-sm)',
            lineHeight: '1.5',
            color: 'var(--text-secondary)',
            fontWeight: '500',
          }}
        >
          {modelRecommendation.reasoning}
        </div>
      </ReasoningBox>
    </PanelContainer>
  );
};

export default QuickDecisionPanel;
