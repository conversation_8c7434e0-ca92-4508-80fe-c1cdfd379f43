/**
 * Elite Intelligence Layout Component
 *
 * Progressive disclosure layout that addresses information overload by providing
 * both quick decision-making and detailed analysis modes. Implements ADHD-optimized
 * design patterns with focus mode toggle and state-aware displays.
 */

import { Card } from '@adhd-trading-dashboard/shared';
import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import { useEnhancedSetupIntelligence } from '../hooks/useEnhancedSetupIntelligence';
import { useGranularSessionIntelligence } from '../hooks/useGranularSessionIntelligence';
import { useModelSelectionEngine } from '../hooks/useModelSelectionEngine';
import { usePatternQualityScoring } from '../hooks/usePatternQualityScoring';
import { useSuccessProbabilityCalculator } from '../hooks/useSuccessProbabilityCalculator';
import { DetailedAnalysisPanel } from './DetailedAnalysisPanel';
import { FocusModeToggle, type FocusMode } from './FocusModeToggle';
import { MarketStateIndicator, getCurrentMarketState } from './MarketStateIndicator';
import { QuickDecisionPanel } from './QuickDecisionPanel';

export interface EliteIntelligenceLayoutProps {
  /** Whether the component is in a loading state */
  isLoading?: boolean;
  /** The error message, if any */
  error?: string | null;
  /** Function called when the refresh button is clicked */
  onRefresh?: () => void;
  /** Additional class name */
  className?: string;
}

// Styled components
const LayoutContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

const HeaderRight = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;

  @media (max-width: 768px) {
    justify-content: space-between;
  }
`;

const RefreshButton = styled.button`
  background: none;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  padding: 8px 12px;
  font-size: 12px;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;

  &:hover {
    background: var(--hover-bg);
    color: var(--text-primary);
    border-color: var(--primary-color);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const ContentContainer = styled.div`
  min-height: 200px;
`;

const LoadingState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: var(--text-secondary);
  text-align: center;
`;

const ErrorState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: var(--error-text);
  text-align: center;
`;

const LoadingSpinner = styled.div`
  width: 32px;
  height: 32px;
  border: 3px solid var(--border-color);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
`;

/**
 * Elite Intelligence Layout Component
 */
export const EliteIntelligenceLayout: React.FC<EliteIntelligenceLayoutProps> = ({
  isLoading = false,
  error = null,
  onRefresh,
  className,
}) => {
  const [focusMode, setFocusMode] = useState<FocusMode>('quick');
  const marketState = getCurrentMarketState();

  // Hook data
  const {
    recommendation: modelRec,
    isLoading: modelLoading,
    error: modelError,
  } = useModelSelectionEngine();

  const {
    analysis: qualityAnalysis,
    isLoading: qualityLoading,
    error: qualityError,
  } = usePatternQualityScoring();

  const { isLoading: sessionLoading, error: sessionError } = useGranularSessionIntelligence();

  const { successProbability, isLoading: probabilityLoading } = useSuccessProbabilityCalculator(
    modelRec,
    qualityAnalysis.currentScore
  );

  const {
    setupIntelligence,
    isLoading: setupLoading,
    error: setupError,
  } = useEnhancedSetupIntelligence();

  // Aggregate loading and error states
  const aggregatedLoading =
    isLoading ||
    modelLoading ||
    qualityLoading ||
    sessionLoading ||
    probabilityLoading ||
    setupLoading;

  const aggregatedError = error || modelError || qualityError || sessionError || setupError;

  // Persist focus mode preference
  useEffect(() => {
    const savedMode = localStorage.getItem('elite-intelligence-focus-mode') as FocusMode;
    if (savedMode && (savedMode === 'quick' || savedMode === 'detailed')) {
      setFocusMode(savedMode);
    }
  }, []);

  const handleFocusModeChange = (newMode: FocusMode) => {
    setFocusMode(newMode);
    localStorage.setItem('elite-intelligence-focus-mode', newMode);
  };

  const handleExpandToDetailed = () => {
    setFocusMode('detailed');
    localStorage.setItem('elite-intelligence-focus-mode', 'detailed');
  };

  // Loading state
  if (aggregatedLoading) {
    return (
      <Card
        title='🧠 Elite ICT Trading Intelligence'
        className={className}
        actions={
          onRefresh && (
            <RefreshButton onClick={onRefresh} disabled={aggregatedLoading}>
              🔄 Refresh
            </RefreshButton>
          )
        }
      >
        <LoadingState>
          <LoadingSpinner />
          <div style={{ fontSize: '16px', fontWeight: '500', marginBottom: '8px' }}>
            Analyzing Market Conditions
          </div>
          <div style={{ fontSize: '14px', opacity: 0.7 }}>
            Generating intelligent trading recommendations...
          </div>
        </LoadingState>
      </Card>
    );
  }

  // Error state
  if (aggregatedError) {
    return (
      <Card
        title='🧠 Elite ICT Trading Intelligence'
        className={className}
        actions={onRefresh && <RefreshButton onClick={onRefresh}>🔄 Retry</RefreshButton>}
      >
        <ErrorState>
          <div style={{ fontSize: '48px', marginBottom: '16px' }}>⚠️</div>
          <div style={{ fontSize: '16px', fontWeight: '500', marginBottom: '8px' }}>
            Error Loading Intelligence Data
          </div>
          <div style={{ fontSize: '14px', opacity: 0.7 }}>{aggregatedError}</div>
        </ErrorState>
      </Card>
    );
  }

  return (
    <Card
      title='🧠 Elite ICT Trading Intelligence'
      className={className}
      actions={
        <HeaderRight>
          <MarketStateIndicator marketState={marketState} showDetails />
          <FocusModeToggle
            mode={focusMode}
            onModeChange={handleFocusModeChange}
            disabled={aggregatedLoading}
          />
          {onRefresh && (
            <RefreshButton onClick={onRefresh} disabled={aggregatedLoading}>
              🔄 Refresh
            </RefreshButton>
          )}
        </HeaderRight>
      }
    >
      <LayoutContainer>
        <ContentContainer>
          {focusMode === 'quick' ? (
            <QuickDecisionPanel
              modelRecommendation={modelRec}
              patternQuality={qualityAnalysis.currentScore}
              successProbability={successProbability}
              setupIntelligence={setupIntelligence}
              onExpandDetails={handleExpandToDetailed}
            />
          ) : (
            <DetailedAnalysisPanel
              modelRecommendation={modelRec}
              patternQuality={qualityAnalysis.currentScore}
              successProbability={successProbability}
              setupIntelligence={setupIntelligence}
            />
          )}
        </ContentContainer>
      </LayoutContainer>
    </Card>
  );
};

export default EliteIntelligenceLayout;
