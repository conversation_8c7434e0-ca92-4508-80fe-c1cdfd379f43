/**
 * Elite Intelligence Layout Component
 *
 * Progressive disclosure layout that addresses information overload by providing
 * quick decision-making with expandable detailed analysis. Implements ADHD-optimized
 * design patterns with expansion toggle and state-aware displays.
 */

import { Card } from '@adhd-trading-dashboard/shared';
import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import { useEnhancedSetupIntelligence } from '../hooks/useEnhancedSetupIntelligence';
import { useGranularSessionIntelligence } from '../hooks/useGranularSessionIntelligence';
import { useModelSelectionEngine } from '../hooks/useModelSelectionEngine';
import { usePatternQualityScoring } from '../hooks/usePatternQualityScoring';
import { useSuccessProbabilityCalculator } from '../hooks/useSuccessProbabilityCalculator';
import { DetailedAnalysisPanel } from './DetailedAnalysisPanel';
import { MarketStateIndicator, getCurrentMarketState } from './MarketStateIndicator';
import { QuickDecisionPanel } from './QuickDecisionPanel';

export interface EliteIntelligenceLayoutProps {
  /** Whether the component is in a loading state */
  isLoading?: boolean;
  /** The error message, if any */
  error?: string | null;
  /** Function called when the refresh button is clicked */
  onRefresh?: () => void;
  /** Additional class name */
  className?: string;
}

// Styled components
const LayoutContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

const HeaderRight = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;

  @media (max-width: 768px) {
    justify-content: space-between;
  }
`;

const RefreshButton = styled.button`
  background: none;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  padding: 8px 12px;
  font-size: 12px;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;

  &:hover {
    background: var(--hover-bg);
    color: var(--text-primary);
    border-color: var(--primary-color);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const ContentContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
`;

const ExpandedContent = styled.div<{ $isExpanded: boolean }>`
  overflow: hidden;
  transition: all 0.3s ease-in-out;
  opacity: ${({ $isExpanded }) => ($isExpanded ? 1 : 0)};
  max-height: ${({ $isExpanded }) => ($isExpanded ? '2000px' : '0')};
  margin-top: ${({ $isExpanded }) => ($isExpanded ? 'var(--spacing-md)' : '0')};
`;

const ExpandToggleButton = styled.button<{ $isExpanded: boolean }>`
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  color: var(--text-primary);
  border: 1px solid var(--primary-color);
  border-radius: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-md);
  font-size: var(--font-size-sm);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  letter-spacing: 0.5px;
  text-transform: uppercase;

  &:hover:not(:disabled) {
    background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
    border-color: var(--accent-color);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }

  &:active {
    transform: translateY(0);
  }

  .expand-icon {
    transition: transform 0.3s ease;
    transform: ${({ $isExpanded }) => ($isExpanded ? 'rotate(180deg)' : 'rotate(0deg)')};
    font-size: var(--font-size-xs);
  }
`;

const LoadingState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: var(--text-secondary);
  text-align: center;
`;

const ErrorState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: var(--error-text);
  text-align: center;
`;

const LoadingSpinner = styled.div`
  width: 32px;
  height: 32px;
  border: 3px solid var(--border-color);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
`;

/**
 * Elite Intelligence Layout Component
 */
export const EliteIntelligenceLayout: React.FC<EliteIntelligenceLayoutProps> = ({
  isLoading = false,
  error = null,
  onRefresh,
  className,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const marketState = getCurrentMarketState();

  // Hook data
  const {
    recommendation: modelRec,
    isLoading: modelLoading,
    error: modelError,
  } = useModelSelectionEngine();

  const {
    analysis: qualityAnalysis,
    isLoading: qualityLoading,
    error: qualityError,
  } = usePatternQualityScoring();

  const { isLoading: sessionLoading, error: sessionError } = useGranularSessionIntelligence();

  const { successProbability, isLoading: probabilityLoading } = useSuccessProbabilityCalculator(
    modelRec,
    qualityAnalysis.currentScore
  );

  const {
    setupIntelligence,
    isLoading: setupLoading,
    error: setupError,
  } = useEnhancedSetupIntelligence();

  // Aggregate loading and error states
  const aggregatedLoading =
    isLoading ||
    modelLoading ||
    qualityLoading ||
    sessionLoading ||
    probabilityLoading ||
    setupLoading;

  const aggregatedError = error || modelError || qualityError || sessionError || setupError;

  // Persist expansion preference
  useEffect(() => {
    const savedExpansion = localStorage.getItem('elite-intelligence-expanded');
    if (savedExpansion === 'true') {
      setIsExpanded(true);
    }
  }, []);

  const handleToggleExpansion = () => {
    const newExpanded = !isExpanded;
    setIsExpanded(newExpanded);
    localStorage.setItem('elite-intelligence-expanded', newExpanded.toString());
  };

  // Loading state
  if (aggregatedLoading) {
    return (
      <Card
        title='🧠 Elite ICT Trading Intelligence'
        className={className}
        actions={
          onRefresh && (
            <RefreshButton onClick={onRefresh} disabled={aggregatedLoading}>
              🔄 Refresh
            </RefreshButton>
          )
        }
      >
        <LoadingState>
          <LoadingSpinner />
          <div style={{ fontSize: '16px', fontWeight: '500', marginBottom: '8px' }}>
            Analyzing Market Conditions
          </div>
          <div style={{ fontSize: '14px', opacity: 0.7 }}>
            Generating intelligent trading recommendations...
          </div>
        </LoadingState>
      </Card>
    );
  }

  // Error state
  if (aggregatedError) {
    return (
      <Card
        title='🧠 Elite ICT Trading Intelligence'
        className={className}
        actions={onRefresh && <RefreshButton onClick={onRefresh}>🔄 Retry</RefreshButton>}
      >
        <ErrorState>
          <div style={{ fontSize: '48px', marginBottom: '16px' }}>⚠️</div>
          <div style={{ fontSize: '16px', fontWeight: '500', marginBottom: '8px' }}>
            Error Loading Intelligence Data
          </div>
          <div style={{ fontSize: '14px', opacity: 0.7 }}>{aggregatedError}</div>
        </ErrorState>
      </Card>
    );
  }

  return (
    <Card
      title='🧠 Elite ICT Trading Intelligence'
      className={className}
      actions={
        <HeaderRight>
          <MarketStateIndicator marketState={marketState} showDetails />
          <ExpandToggleButton
            $isExpanded={isExpanded}
            onClick={handleToggleExpansion}
            disabled={aggregatedLoading}
          >
            {isExpanded ? '📊 Collapse' : '📊 Detailed Analysis'}
            <span className='expand-icon'>▼</span>
          </ExpandToggleButton>
          {onRefresh && (
            <RefreshButton onClick={onRefresh} disabled={aggregatedLoading}>
              🔄 Refresh
            </RefreshButton>
          )}
        </HeaderRight>
      }
    >
      <LayoutContainer>
        <ContentContainer>
          {/* Always show Quick Decision Panel */}
          <QuickDecisionPanel
            modelRecommendation={modelRec}
            patternQuality={qualityAnalysis.currentScore}
            successProbability={successProbability}
            setupIntelligence={setupIntelligence}
          />

          {/* Expandable Detailed Analysis */}
          <ExpandedContent $isExpanded={isExpanded}>
            <DetailedAnalysisPanel
              modelRecommendation={modelRec}
              patternQuality={qualityAnalysis.currentScore}
              successProbability={successProbability}
              setupIntelligence={setupIntelligence}
            />
          </ExpandedContent>
        </ContentContainer>
      </LayoutContainer>
    </Card>
  );
};

export default EliteIntelligenceLayout;
